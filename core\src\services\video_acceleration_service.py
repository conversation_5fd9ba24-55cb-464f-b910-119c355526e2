"""
视频加速服务
批量将视频加速到指定时长
"""

import os
import asyncio
import subprocess
import logging
import time
from typing import List, Dict, Any, Tuple

logger = logging.getLogger(__name__)

class VideoAccelerationService:
    """视频加速服务类"""

    def __init__(self):
        self.supported_video_extensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v']

    async def get_video_duration(self, video_path: str) -> float:
        """获取视频时长（秒）"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', video_path
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode != 0:
                logger.warning(f"无法获取视频时长: {video_path}")
                return 0.0
            
            if not result.stdout or result.stdout.strip() == '':
                logger.warning(f"ffprobe返回空结果: {video_path}")
                return 0.0
            
            import json
            data = json.loads(result.stdout)
            
            if 'format' in data and 'duration' in data['format']:
                duration = float(data['format']['duration'])
                logger.debug(f"视频时长: {video_path} = {duration}秒")
                return duration
            else:
                logger.warning(f"无法从ffprobe结果中获取时长: {video_path}")
                return 0.0
                
        except Exception as e:
            logger.error(f"获取视频时长失败 {video_path}: {str(e)}")
            return 0.0

    def calculate_speed_factor(self, original_duration: float, target_duration: int) -> float:
        """计算加速倍率"""
        if original_duration <= 0 or target_duration <= 0:
            return 1.0
        
        # 如果原始时长已经小于等于目标时长，不需要加速
        if original_duration <= target_duration:
            return 1.0
        
        # 计算加速倍率
        speed_factor = original_duration / target_duration
        
        # 限制最大加速倍率为10倍，避免过度加速
        if speed_factor > 10.0:
            speed_factor = 10.0
            logger.warning(f"加速倍率超过10倍，限制为10倍: {speed_factor}")
        
        return speed_factor

    async def accelerate_single_video(self, input_path: str, output_path: str, 
                                    target_duration: int, output_quality: str) -> Dict[str, Any]:
        """加速单个视频文件"""
        start_time = time.time()
        
        try:
            # 检查输入文件是否存在
            if not os.path.exists(input_path):
                raise FileNotFoundError(f"输入文件不存在: {input_path}")
            
            # 获取原始文件大小和时长
            original_file_size = os.path.getsize(input_path)
            original_duration = await self.get_video_duration(input_path)
            
            if original_duration <= 0:
                raise RuntimeError("无法获取视频时长")
            
            # 计算加速倍率
            speed_factor = self.calculate_speed_factor(original_duration, target_duration)
            
            if speed_factor == 1.0:
                # 不需要加速，直接复制文件
                if input_path != output_path:
                    import shutil
                    shutil.copy2(input_path, output_path)
                
                processing_time = int((time.time() - start_time) * 1000)
                logger.info(f"视频无需加速: {output_path}, 耗时: {processing_time}ms")
                
                return {
                    'success': True,
                    'original_path': input_path,
                    'output_path': output_path,
                    'error_message': '',
                    'processing_time_ms': processing_time,
                    'original_file_size': original_file_size,
                    'output_file_size': original_file_size,
                    'original_duration': original_duration,
                    'output_duration': original_duration,
                    'speed_factor': speed_factor
                }
            
            # 构建ffmpeg命令
            cmd = ['ffmpeg', '-y', '-i', input_path]
            
            # 添加视频加速滤镜
            cmd.extend(['-vf', f'setpts={1/speed_factor}*PTS'])
            
            # 添加音频加速滤镜
            cmd.extend(['-af', f'atempo={speed_factor}'])
            
            # 设置编码参数
            cmd.extend(['-c:v', 'libx264', '-preset', 'fast'])
            
            # 根据质量设置CRF
            if output_quality == 'high':
                cmd.extend(['-crf', '18'])
            elif output_quality == 'medium':
                cmd.extend(['-crf', '23'])
            else:  # low
                cmd.extend(['-crf', '28'])
            
            # 音频编码
            cmd.extend(['-c:a', 'aac', '-b:a', '128k'])
            
            # 输出文件
            cmd.append(output_path)
            
            logger.debug(f"执行ffmpeg命令: {' '.join(cmd[:10])}...")
            
            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode('utf-8', errors='ignore') if stderr else "未知错误"
                logger.error(f"ffmpeg执行失败: {error_msg}")
                raise RuntimeError(f"视频加速失败: {error_msg}")
            
            # 检查输出文件是否生成
            if not os.path.exists(output_path):
                raise RuntimeError("输出文件未生成")
            
            # 获取输出文件大小和时长
            output_file_size = os.path.getsize(output_path)
            output_duration = await self.get_video_duration(output_path)
            
            processing_time = int((time.time() - start_time) * 1000)
            
            logger.info(f"视频加速成功: {output_path}, 加速倍率: {speed_factor:.2f}x, 耗时: {processing_time}ms")
            
            return {
                'success': True,
                'original_path': input_path,
                'output_path': output_path,
                'error_message': '',
                'processing_time_ms': processing_time,
                'original_file_size': original_file_size,
                'output_file_size': output_file_size,
                'original_duration': original_duration,
                'output_duration': output_duration,
                'speed_factor': speed_factor
            }
            
        except Exception as e:
            processing_time = int((time.time() - start_time) * 1000)
            error_msg = str(e)
            logger.error(f"加速视频失败 {input_path}: {error_msg}", exc_info=True)
            
            return {
                'success': False,
                'original_path': input_path,
                'output_path': output_path,
                'error_message': error_msg,
                'processing_time_ms': processing_time,
                'original_file_size': 0,
                'output_file_size': 0,
                'original_duration': 0.0,
                'output_duration': 0.0,
                'speed_factor': 0.0
            }

    def generate_output_path(self, input_path: str, overwrite_original: bool,
                           output_suffix: str) -> str:
        """生成输出文件路径"""
        if overwrite_original:
            return input_path

        # 获取输入文件的目录和文件名
        input_dir = os.path.dirname(input_path)
        input_filename = os.path.basename(input_path)

        # 创建输出目录
        output_dir = os.path.join(input_dir, "accelerated_videos")
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            logger.info(f"创建输出目录: {output_dir}")

        # 生成输出文件路径
        path_parts = os.path.splitext(input_filename)
        output_filename = f"{path_parts[0]}{output_suffix}{path_parts[1]}"
        return os.path.join(output_dir, output_filename)

    async def accelerate_videos(self, video_paths: List[str], target_duration: int = 59,
                              output_quality: str = 'medium', overwrite_original: bool = False,
                              output_suffix: str = '_accelerated') -> Dict[str, Any]:
        """批量加速视频"""
        start_time = time.time()
        
        try:
            logger.info(f"开始批量视频加速:")
            logger.info(f"  - 视频文件数量: {len(video_paths)}")
            logger.info(f"  - 目标时长: {target_duration}秒")
            logger.info(f"  - 输出质量: {output_quality}")
            logger.info(f"  - 覆盖原文件: {overwrite_original}")
            logger.info(f"  - 输出后缀: {output_suffix}")
            if not overwrite_original:
                logger.info(f"  - 输出文件将保存到 accelerated_videos 子文件夹")
            
            # 验证输入参数
            if not video_paths:
                return {
                    "success": False,
                    "error": "未提供视频文件路径",
                    "results": [],
                    "total_processing_time_ms": 0,
                    "successful_count": 0,
                    "failed_count": 0
                }
            
            if target_duration <= 0:
                return {
                    "success": False,
                    "error": "目标时长必须大于0",
                    "results": [],
                    "total_processing_time_ms": 0,
                    "successful_count": 0,
                    "failed_count": 0
                }
            
            # 检查文件是否存在
            missing_files = []
            for video_path in video_paths:
                if not os.path.exists(video_path):
                    missing_files.append(video_path)
            
            if missing_files:
                return {
                    "success": False,
                    "error": f"以下文件不存在: {', '.join(missing_files)}",
                    "results": [],
                    "total_processing_time_ms": 0,
                    "successful_count": 0,
                    "failed_count": 0
                }
            
            # 批量处理视频
            results = []
            successful_count = 0
            failed_count = 0
            
            # 使用信号量控制并发数量，避免系统负载过高
            semaphore = asyncio.Semaphore(3)  # 最多同时处理3个视频
            
            async def process_single_video(video_path: str):
                async with semaphore:
                    output_path = self.generate_output_path(
                        video_path, overwrite_original, output_suffix
                    )
                    return await self.accelerate_single_video(
                        video_path, output_path, target_duration, output_quality
                    )
            
            # 并发处理所有视频
            tasks = [process_single_video(video_path) for video_path in video_paths]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    # 处理异常情况
                    error_result = {
                        'success': False,
                        'original_path': video_paths[i],
                        'output_path': '',
                        'error_message': str(result),
                        'processing_time_ms': 0,
                        'original_file_size': 0,
                        'output_file_size': 0,
                        'original_duration': 0.0,
                        'output_duration': 0.0,
                        'speed_factor': 0.0
                    }
                    processed_results.append(error_result)
                    failed_count += 1
                else:
                    processed_results.append(result)
                    if result['success']:
                        successful_count += 1
                    else:
                        failed_count += 1
            
            total_processing_time = int((time.time() - start_time) * 1000)
            
            logger.info(f"批量视频加速完成:")
            logger.info(f"  - 总耗时: {total_processing_time}ms")
            logger.info(f"  - 成功: {successful_count}个")
            logger.info(f"  - 失败: {failed_count}个")
            
            return {
                "success": True,
                "error": "",
                "results": processed_results,
                "total_processing_time_ms": total_processing_time,
                "successful_count": successful_count,
                "failed_count": failed_count
            }
            
        except Exception as e:
            total_processing_time = int((time.time() - start_time) * 1000)
            error_msg = str(e)
            logger.error(f"批量视频加速失败: {error_msg}", exc_info=True)
            
            return {
                "success": False,
                "error": error_msg,
                "results": [],
                "total_processing_time_ms": total_processing_time,
                "successful_count": 0,
                "failed_count": len(video_paths) if video_paths else 0
            }
