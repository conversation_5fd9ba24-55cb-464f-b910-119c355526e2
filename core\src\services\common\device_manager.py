"""
设备管理器
负责设备连接、设备ID映射等基础功能
"""

import os
import logging
import asyncio
import json
import yaml
from typing import Dict, Any, Optional
import urllib3

from appium import webdriver
from appium.webdriver.common.appiumby import AppiumBy
from .resource_manager import resource_manager
from .adb_manager import get_adb_manager

# 增加urllib3连接池大小，避免连接池满的警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
# 配置连接池
import urllib3.util.connection
original_create_connection = urllib3.util.connection.create_connection

# 导入Appium选项
try:
    from appium.options.android import UiAutomator2Options
except ImportError:
    # 如果导入失败，创建一个兼容的选项类
    class UiAutomator2Options:
        def __init__(self):
            self.capabilities = {}

        def to_capabilities(self):
            return self.capabilities

        def __setattr__(self, name, value):
            if name == 'capabilities':
                super().__setattr__(name, value)
            else:
                # 转换驼峰命名为下划线命名
                key = ''.join(['_' + c.lower() if c.isupper() else c for c in name]).lstrip('_')
                # 添加appium:前缀
                if key not in ['platform_name', 'automation_name']:
                    key = 'appium:' + key
                self.capabilities[key] = value

logger = logging.getLogger(__name__)


class DeviceManager:
    """设备管理器类"""

    def __init__(self, device_id: str, appium_server: str = 'http://localhost:4723'):
        """初始化设备管理器

        Args:
            device_id: 设备ID
            appium_server: Appium服务器地址
        """
        self.device_id = device_id  # 原始设备ID（如"18"）
        self.real_device_id = None  # 映射后的设备ID（如"emulator-5590"）
        # 检查Appium服务器URL格式
        if appium_server.endswith('/wd/hub'):
            # Appium 1.x格式
            self.appium_server = appium_server
        elif not appium_server.endswith('/'):
            # Appium 2.x格式，确保URL末尾没有斜杠
            self.appium_server = appium_server
        else:
            # 移除末尾的斜杠
            self.appium_server = appium_server.rstrip('/')

        self.driver = None

        # 配置urllib3连接池，避免连接池满的警告
        self._configure_connection_pool()

    def _configure_connection_pool(self):
        """配置urllib3连接池，增加连接池大小"""
        try:
            # 方法1：配置urllib3的默认连接池参数
            import urllib3.poolmanager
            import urllib3.util.retry

            # 禁用连接池警告
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

            # 设置默认连接池参数
            if hasattr(urllib3.poolmanager.PoolManager, 'DEFAULTS'):
                urllib3.poolmanager.PoolManager.DEFAULTS = urllib3.poolmanager.PoolManager.DEFAULTS.copy()
                urllib3.poolmanager.PoolManager.DEFAULTS.update({
                    'maxsize': 20,  # 增加连接池大小到20
                    'block': False,  # 不阻塞
                })

            # 方法2：直接设置环境变量（对某些版本的urllib3有效）
            import os
            os.environ['URLLIB3_POOL_MAXSIZE'] = '20'

            logger.info("✅ 已配置urllib3连接池，最大连接数: 20")

        except Exception as e:
            logger.warning(f"配置连接池失败: {str(e)}")

        # 方法3：尝试直接配置Appium的连接池
        try:
            from selenium.webdriver.remote.remote_connection import RemoteConnection
            import urllib3.util.retry

            # 创建一个函数来替换连接管理器
            def get_custom_connection_manager():
                return urllib3.PoolManager(
                    maxsize=20,
                    block=False,
                    retries=urllib3.util.retry.Retry(total=3)
                )

            # 替换连接管理器方法
            def custom_get_connection_manager(self):
                return get_custom_connection_manager()

            RemoteConnection._get_connection_manager = custom_get_connection_manager
            logger.info("✅ 已配置Appium连接池")
        except Exception as e:
            logger.debug(f"配置Appium连接池失败: {str(e)}")

    async def connect(self, app_package: str = None, app_activity: str = None) -> bool:
        """连接到设备

        Args:
            app_package: 应用包名（可选）
            app_activity: 应用活动名（可选）

        Returns:
            bool: 是否连接成功
        """
        try:
            # 获取真实设备ID
            real_device_id = await self.get_real_device_id(self.device_id)
            if not real_device_id:
                logger.error(f"无法获取设备 {self.device_id} 的真实ID，连接失败")
                return False

            logger.info(f"使用设备ID: {real_device_id}")

            # 创建Appium选项对象
            options = UiAutomator2Options()
            options.platform_name = 'Android'
            options.device_name = real_device_id
            options.udid = real_device_id
            options.automation_name = 'UiAutomator2'
            options.platform_version = '9.0'

            # 如果指定了应用包名和活动名，则设置
            if app_package and app_activity:
                options.app_package = app_package
                options.app_activity = app_activity
                options.capabilities['appium:forceAppLaunch'] = True

            options.no_reset = True
            options.fast_reset = False
            options.auto_grant_permissions = True
            options.new_command_timeout = 300

            # 🔧 新增：UiAutomator2 稳定性配置
            options.capabilities['appium:uiautomator2ServerLaunchTimeout'] = 60000  # 60秒启动超时
            options.capabilities['appium:uiautomator2ServerInstallTimeout'] = 60000  # 60秒安装超时
            options.capabilities['appium:uiautomator2ServerReadTimeout'] = 120000   # 120秒读取超时
            options.capabilities['appium:androidInstallTimeout'] = 90000           # 90秒安装超时
            options.capabilities['appium:adbExecTimeout'] = 60000                  # 60秒ADB执行超时

            # 🔧 防止内存问题
            options.capabilities['appium:clearSystemFiles'] = True                 # 清理系统文件
            options.capabilities['appium:enforceXPath1'] = True                    # 使用XPath1（更稳定）
            options.capabilities['appium:disableWindowAnimation'] = True           # 禁用窗口动画

            # 🔧 增强稳定性
            options.capabilities['appium:skipServerInstallation'] = False          # 确保服务器安装
            options.capabilities['appium:ignoreUnimportantViews'] = True           # 忽略不重要的视图
            options.capabilities['appium:waitForIdleTimeout'] = 10000              # 等待空闲超时10秒

            # 基本超时设置
            options.capabilities['appium:newCommandTimeout'] = 300                 # 5分钟命令超时

            # 连接到Appium服务器
            logger.info(f"正在连接到Appium服务器: {self.appium_server}")
            logger.info(f"使用配置: {options.to_capabilities()}")

            # 创建WebDriver实例
            try:
                # 方法1：使用options方式（推荐，适用于新版本）
                logger.info("尝试使用options方式创建驱动...")
                self.driver = resource_manager.register_webdriver(
                    webdriver.Remote(self.appium_server, options=options)
                )
                logger.info(f"✅ 使用options方式成功创建驱动")

            except Exception as options_error:
                logger.warning(f"options方式失败: {str(options_error)}")
                try:
                    # 方法2：使用capabilities字典方式（适用于中等版本）
                    logger.info("尝试使用capabilities字典方式创建驱动...")
                    capabilities = options.to_capabilities()

                    # 检查webdriver.Remote的构造函数参数
                    import inspect
                    remote_signature = inspect.signature(webdriver.Remote.__init__)
                    params = list(remote_signature.parameters.keys())
                    logger.info(f"WebDriver.Remote支持的参数: {params}")

                    if 'desired_capabilities' in params:
                        # 旧版本API
                        self.driver = webdriver.Remote(self.appium_server, desired_capabilities=capabilities)
                        logger.info(f"✅ 使用desired_capabilities方式成功创建驱动")
                    elif 'capabilities' in params:
                        # 中等版本API
                        self.driver = webdriver.Remote(self.appium_server, capabilities=capabilities)
                        logger.info(f"✅ 使用capabilities方式成功创建驱动")
                    else:
                        # 尝试直接传递capabilities
                        self.driver = webdriver.Remote(self.appium_server, **capabilities)
                        logger.info(f"✅ 使用展开capabilities方式成功创建驱动")

                except Exception as capabilities_error:
                    logger.error(f"capabilities方式也失败: {str(capabilities_error)}")
                    try:
                        # 方法3：使用最基本的方式
                        logger.info("尝试使用基本方式创建驱动...")
                        basic_caps = {
                            'platformName': 'Android',
                            'deviceName': real_device_id,
                            'udid': real_device_id,
                            'automationName': 'UiAutomator2',
                            'noReset': True,
                            'autoGrantPermissions': True,
                            'newCommandTimeout': 300
                        }

                        # 尝试不同的参数传递方式
                        try:
                            self.driver = webdriver.Remote(self.appium_server, options=UiAutomator2Options().load_capabilities(basic_caps))
                        except:
                            try:
                                # 创建基本的options对象
                                basic_options = UiAutomator2Options()
                                for key, value in basic_caps.items():
                                    setattr(basic_options, key, value)
                                self.driver = webdriver.Remote(self.appium_server, options=basic_options)
                            except:
                                # 最后的尝试
                                self.driver = webdriver.Remote(self.appium_server)

                        logger.info(f"✅ 使用基本方式成功创建驱动")

                    except Exception as basic_error:
                        logger.error(f"所有创建方式都失败: {str(basic_error)}")
                        raise options_error

            # 验证驱动
            logger.info(f"已连接到设备: {real_device_id}")
            logger.info(f"驱动类型: {type(self.driver)}")
            logger.info(f"支持的方法: start_activity={hasattr(self.driver, 'start_activity')}, terminate_app={hasattr(self.driver, 'terminate_app')}")

            # 如果驱动缺少必要方法，记录警告但继续
            if not hasattr(self.driver, 'start_activity'):
                logger.warning("⚠️ 驱动缺少start_activity方法，某些功能可能受限")
            if not hasattr(self.driver, 'terminate_app'):
                logger.warning("⚠️ 驱动缺少terminate_app方法，某些功能可能受限")

            # 🔧 重要修复：不要覆盖原始设备ID，保存映射后的设备ID到单独的属性
            # self.device_id保持原始值（如"18"），用于后续的映射查找
            # self.real_device_id保存映射后的值（如"emulator-5590"），用于Appium连接
            self.real_device_id = real_device_id
            logger.info(f"✅ 设备ID映射完成: {self.device_id} -> {self.real_device_id}")
            return True

        except Exception as e:
            logger.error(f"连接设备异常: {str(e)}", exc_info=True)
            return False

    async def disconnect(self) -> None:
        """断开连接"""
        try:
            if self.driver:
                logger.info("正在断开设备连接...")

                # 尝试优雅地关闭会话
                try:
                    # 从资源管理器注销
                    resource_manager.unregister_webdriver(self.driver)
                    self.driver.quit()
                    logger.info("✅ 已优雅地关闭WebDriver会话")
                except Exception as quit_error:
                    logger.warning(f"优雅关闭失败，尝试强制关闭: {str(quit_error)}")
                    try:
                        # 强制关闭连接
                        if hasattr(self.driver, 'close'):
                            self.driver.close()
                        if hasattr(self.driver, 'command_executor') and hasattr(self.driver.command_executor, 'close'):
                            self.driver.command_executor.close()
                    except Exception as force_error:
                        logger.warning(f"强制关闭也失败: {str(force_error)}")

                self.driver = None
                logger.info("✅ 设备连接已断开")

                # 等待一小段时间，让连接完全释放
                await asyncio.sleep(0.5)

        except Exception as e:
            logger.error(f"断开连接异常: {str(e)}", exc_info=True)
            # 即使出现异常，也要清空driver引用
            self.driver = None

    async def get_real_device_id(self, device_id: str) -> str:
        """获取真实的设备ID（ADB ID）

        Args:
            device_id: 雷电模拟器设备ID

        Returns:
            str: 真实的设备ID（ADB ID）
        """
        try:
            # 首先尝试从配置文件获取设备映射关系
            mapping_config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'device_mapping.yaml')
            logger.info(f"尝试从配置文件获取设备映射: {mapping_config_path}")

            if os.path.exists(mapping_config_path):
                try:
                    with open(mapping_config_path, 'r', encoding='utf-8') as f:
                        config = yaml.safe_load(f)

                    if config and 'mappings' in config:
                        mappings = config['mappings']
                        selection_strategy = config.get('selection_strategy', 'strict')

                        logger.info(f"从配置文件加载设备映射: {mappings}")
                        logger.info(f"设备选择策略: {selection_strategy}")

                        # 检查是否有对应的映射
                        if device_id in mappings:
                            real_id = mappings[device_id]
                            logger.info(f"从配置文件获取设备映射: {device_id} -> {real_id}")
                            return real_id
                        else:
                            logger.warning(f"配置文件中没有设备 {device_id} 的映射")

                            # 根据策略决定后续行为
                            if selection_strategy == 'strict':
                                logger.error(f"严格模式下，未找到设备 {device_id} 的映射，无法继续")
                                return None

                except Exception as config_error:
                    logger.error(f"读取配置文件异常: {str(config_error)}", exc_info=True)
            else:
                logger.warning(f"设备映射配置文件不存在: {mapping_config_path}")

            # 如果没有从配置文件获取到映射，尝试从环境变量获取
            device_mapping = os.environ.get("DEVICE_MAPPING", None)
            if device_mapping:
                try:
                    mapping = json.loads(device_mapping)
                    if device_id in mapping:
                        real_id = mapping[device_id]
                        logger.info(f"从环境变量获取设备映射: {device_id} -> {real_id}")
                        return real_id
                except json.JSONDecodeError:
                    logger.warning(f"解析环境变量DEVICE_MAPPING失败: {device_mapping}")

            # 如果没有找到映射，返回None表示失败
            logger.error(f"未找到设备 {device_id} 的映射，无法获取真实设备ID")
            return None

        except Exception as e:
            logger.error(f"获取真实设备ID异常: {str(e)}", exc_info=True)
            # 出现异常时返回None
            logger.error(f"获取设备ID异常，无法继续")
            return None

    async def launch_app(self, app_package: str, app_activity: str, max_retries: int = 3) -> bool:
        """启动指定应用

        Args:
            app_package: 应用包名
            app_activity: 应用活动名
            max_retries: 最大重试次数

        Returns:
            bool: 是否成功启动
        """
        # 简单检查应用是否已经在运行（不强制要求）
        if self.driver:
            try:
                current_package = self.driver.current_package
                if current_package == app_package:
                    logger.info(f"✅ 应用 {app_package} 已经在运行")
                    return True
            except Exception as e:
                logger.debug(f"检查当前应用状态失败: {str(e)}")

        for attempt in range(max_retries):
            try:
                logger.info(f"启动应用: {app_package}/{app_activity} (尝试 {attempt + 1}/{max_retries})")

                # 🔧 新增：检查 ADB 连接状态
                adb_manager = get_adb_manager()
                # 使用真实设备ID进行ADB连接检查
                device_id_for_adb = self.real_device_id if self.real_device_id else self.device_id
                if not await adb_manager.ensure_device_connected(device_id_for_adb):
                    logger.error(f"❌ 设备 {device_id_for_adb} ADB 连接异常，无法启动应用")
                    if attempt < max_retries - 1:
                        logger.info("等待10秒后重试...")
                        await asyncio.sleep(10)
                        continue
                    else:
                        return False

                # 如果有现有的驱动，先尝试使用它启动应用
                if self.driver and attempt == 0:
                    try:
                        logger.info("尝试使用现有会话启动应用...")

                        # 检查驱动是否有必要的方法
                        if hasattr(self.driver, 'start_activity'):
                            # 启动应用
                            self.driver.start_activity(app_package, app_activity)
                            await asyncio.sleep(5)

                            # 验证应用是否启动成功
                            current_package = self.driver.current_package
                            if current_package == app_package:
                                logger.info(f"✅ 使用现有会话成功启动应用: {app_package}")
                                return True
                            else:
                                logger.warning(f"现有会话启动失败，当前包名: {current_package}")
                        else:
                            logger.warning("当前驱动不支持start_activity方法，跳过现有会话")
                            raise AttributeError("Driver does not support start_activity")
                    except Exception as e:
                        logger.warning(f"使用现有会话启动失败: {str(e)}")
                        # 如果现有会话有问题，清理它
                        try:
                            if self.driver:
                                self.driver.quit()
                                self.driver = None
                                logger.info("已清理有问题的现有会话")
                        except:
                            pass

                # 创建新的连接配置
                logger.info("创建新的Appium会话...")
                options = UiAutomator2Options()
                options.platform_name = 'Android'
                # 使用真实设备ID进行Appium连接
                device_id_for_appium = self.real_device_id if self.real_device_id else self.device_id
                options.device_name = device_id_for_appium
                options.udid = device_id_for_appium
                options.automation_name = 'UiAutomator2'
                options.platform_version = '9.0'
                options.app_package = app_package
                options.app_activity = app_activity
                options.no_reset = True
                options.fast_reset = False
                options.auto_grant_permissions = True
                options.new_command_timeout = 300
                options.capabilities['appium:forceAppLaunch'] = True

                # 🔧 新增：UiAutomator2 稳定性配置（重连时也使用）
                options.capabilities['appium:uiautomator2ServerLaunchTimeout'] = 60000
                options.capabilities['appium:uiautomator2ServerInstallTimeout'] = 60000
                options.capabilities['appium:uiautomator2ServerReadTimeout'] = 120000
                options.capabilities['appium:androidInstallTimeout'] = 90000
                options.capabilities['appium:adbExecTimeout'] = 60000
                options.capabilities['appium:clearSystemFiles'] = True
                options.capabilities['appium:enforceXPath1'] = True
                options.capabilities['appium:disableWindowAnimation'] = True
                options.capabilities['appium:skipServerInstallation'] = False
                options.capabilities['appium:ignoreUnimportantViews'] = True
                options.capabilities['appium:waitForIdleTimeout'] = 10000
                options.capabilities['appium:newCommandTimeout'] = 300
                options.capabilities['appium:keepAliveTimeout'] = 600
                options.capabilities['appium:uiautomator2ServerSocketTimeout'] = 120000
                options.capabilities['appium:mjpegServerPort'] = None

                # 先关闭当前会话
                if self.driver:
                    try:
                        self.driver.quit()
                        await asyncio.sleep(1)
                    except:
                        pass

                # 创建新会话
                try:
                    self.driver = resource_manager.register_webdriver(
                        webdriver.Remote(self.appium_server, options=options)
                    )
                    logger.info(f"✅ 使用options方式创建新会话成功")
                except Exception as session_error:
                    logger.warning(f"options方式创建会话失败: {str(session_error)}")
                    try:
                        # 尝试基本方式
                        basic_options = UiAutomator2Options()
                        basic_options.platform_name = 'Android'
                        # 使用真实设备ID进行Appium连接
                        device_id_for_appium = self.real_device_id if self.real_device_id else self.device_id
                        basic_options.device_name = device_id_for_appium
                        basic_options.udid = device_id_for_appium
                        basic_options.automation_name = 'UiAutomator2'
                        basic_options.app_package = app_package
                        basic_options.app_activity = app_activity
                        basic_options.no_reset = True
                        basic_options.auto_grant_permissions = True
                        basic_options.new_command_timeout = 300

                        self.driver = resource_manager.register_webdriver(
                            webdriver.Remote(self.appium_server, options=basic_options)
                        )
                        logger.info(f"✅ 使用基本方式创建新会话成功")
                    except Exception as basic_session_error:
                        logger.error(f"所有会话创建方式都失败: {str(basic_session_error)}")
                        raise session_error

                # 等待应用启动
                await asyncio.sleep(5)

                # 验证应用是否启动成功
                try:
                    current_package = self.driver.current_package
                    if current_package == app_package:
                        logger.info(f"✅ 成功启动应用: {app_package}")
                        return True
                    else:
                        logger.warning(f"应用启动验证失败，当前包名: {current_package}")
                        if attempt < max_retries - 1:
                            logger.info("等待3秒后重试...")
                            await asyncio.sleep(3)
                except Exception as verify_e:
                    logger.warning(f"验证应用启动状态失败: {str(verify_e)}")
                    if attempt < max_retries - 1:
                        logger.info("等待3秒后重试...")
                        await asyncio.sleep(3)

            except Exception as e:
                logger.error(f"启动应用异常 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt < max_retries - 1:
                    logger.info("等待3秒后重试...")
                    await asyncio.sleep(3)

        logger.error(f"❌ 所有启动尝试都失败，无法启动应用: {app_package}")
        return False

    def get_driver(self):
        """获取Appium驱动"""
        return self.driver

    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.driver is not None
