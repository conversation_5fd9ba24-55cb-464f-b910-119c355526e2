<template>
  <div class="file-manager">
    <!-- 页面头部 - 按照现有系统风格 -->
    <div class="management-header">
      <h1>📂 文件管理</h1>
      <p class="header-description">管理和浏览发布内容文件，支持多Core服务和平台文件管理</p>
    </div>

    <!-- 统计卡片 - 按照任务管理页面的风格 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-card class="stat-card total">
            <div class="stat-content">
              <div class="stat-icon">📊</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.total_count }}</div>
                <div class="stat-label">总内容</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card video">
            <div class="stat-content">
              <div class="stat-icon">🎬</div>
              <div class="stat-info">
                <div class="stat-number">{{ getTypeCount('video') }}</div>
                <div class="stat-label">视频</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card image">
            <div class="stat-content">
              <div class="stat-icon">🖼️</div>
              <div class="stat-info">
                <div class="stat-number">{{ getTypeCount('image') }}</div>
                <div class="stat-label">图片</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card audio">
            <div class="stat-content">
              <div class="stat-icon">🎵</div>
              <div class="stat-info">
                <div class="stat-number">{{ getTypeCount('audio') }}</div>
                <div class="stat-label">音频</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card recent">
            <div class="stat-content">
              <div class="stat-icon">🆕</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.recent_count }}</div>
                <div class="stat-label">最近新增</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card downloading">
            <div class="stat-content">
              <div class="stat-icon">⬇️</div>
              <div class="stat-info">
                <div class="stat-number">{{ downloadingCount }}</div>
                <div class="stat-label">下载中</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- Core服务状态 -->
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="24">
          <el-card class="stat-card core-services">
            <div class="stat-content">
              <div class="stat-icon">🖥️</div>
              <div>
                <div class="stat-number">{{ coreServices.length }}</div>
                <div class="stat-label">Core服务</div>
              </div>
              <div class="core-services-detail">
                <el-tag
                  v-for="service in coreServices"
                  :key="service.id"
                  size="small"
                  :type="getCoreServiceStatusColor(service.status || 'unknown')"
                  style="margin-left: 8px;"
                >
                  {{ service.name }} ({{ getCoreServiceStatusText(service.status || 'unknown') }})
                </el-tag>
                <span v-if="coreServices.length === 0" style="color: #f56c6c; margin-left: 8px;">
                  无可用服务 - 请检查Core服务是否正常运行
                </span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 操作工具栏 - 按照现有系统风格 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="showCreateTaskDialog = true">
          <el-icon><Download /></el-icon>
          新建下载任务
        </el-button>

        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>

        <el-button @click="goToParentDirectory" :disabled="currentFolderPath === 'H:\\PublishSystem'">
          ⬆️ 上级目录
        </el-button>

        <!-- 发布处理工具下拉菜单 -->
        <el-dropdown @command="handlePublishCommand" type="primary">
          <el-button type="primary">
            📤 发布处理 <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="info">
                🔍 {{ showAdvancedInfo ? '隐藏详细信息' : '显示详细信息' }}
              </el-dropdown-item>
              <el-dropdown-item divided command="rename">✏️ 批量改名</el-dropdown-item>
              <el-dropdown-item command="md5">🔐 MD5记录管理</el-dropdown-item>
              <el-dropdown-item
                command="publish"
                :disabled="selectedItems.filter(f => !f.is_directory && f.md5_hash).length === 0"
              >
                📤 批量设置发布状态
              </el-dropdown-item>
              <el-dropdown-item command="archive">📦 一键归档已发布</el-dropdown-item>
              <el-dropdown-item divided command="category">📁 管理分类</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 视频处理下拉菜单 -->
        <el-dropdown @command="handleVideoProcessCommand" type="success">
          <el-button type="success">
            🎬 视频处理 <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="merge">🎬 批量合并视频</el-dropdown-item>
              <el-dropdown-item command="triple">📱 三拼视频</el-dropdown-item>
              <el-dropdown-item command="watermark">🚫 去水印</el-dropdown-item>
              <el-dropdown-item command="rotate" :disabled="selectedVideoFiles.length === 0">🔄 旋转视频</el-dropdown-item>
              <el-dropdown-item command="accelerate" :disabled="selectedVideoFiles.length === 0">⚡ 视频加速</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 音频处理下拉菜单 -->
        <el-dropdown @command="handleAudioProcessCommand" type="warning">
          <el-button type="warning">
            🎵 音频处理 <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="subtitles">📝 批量生成字幕</el-dropdown-item>
              <el-dropdown-item command="extract">🎵 批量分离音频</el-dropdown-item>
              <el-dropdown-item command="vocals">🎤 批量人声分离</el-dropdown-item>
              <el-dropdown-item command="replace">🔄 批量替换音频</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>


      </div>
      <div class="toolbar-right">
        <!-- Core服务选择 -->
        <el-select
          v-model="selectedCoreService"
          placeholder="选择Core服务"
          style="width: 180px"
          @change="loadFileList"
        >
          <el-option label="所有Core服务" value="" />
          <el-option
            v-for="service in coreServices"
            :key="service.id"
            :value="service.id"
          >
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>{{ service.name }}</span>
              <el-tag
                :type="getCoreServiceStatusColor(service.status || 'unknown')"
                size="small"
              >
                {{ getCoreServiceStatusText(service.status || 'unknown') }}
              </el-tag>
            </div>
          </el-option>
        </el-select>

        <el-select v-model="filterPlatform" placeholder="平台筛选" style="width: 120px; margin-left: 10px" @change="loadFileList">
          <el-option label="全部" value="" />
          <el-option label="YouTube" value="youtube" />
          <el-option label="TikTok" value="tiktok" />
          <el-option label="Instagram" value="instagram" />
        </el-select>
        <el-select v-model="filterContentType" placeholder="类型筛选" style="width: 120px; margin-left: 10px" @change="loadFileList">
          <el-option label="全部" value="" />
          <el-option label="视频" value="video" />
          <el-option label="图片" value="image" />
          <el-option label="音频" value="audio" />
        </el-select>
        <el-input
          v-model="searchQuery"
          placeholder="搜索内容..."
          style="width: 200px; margin-left: 10px"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 当前路径显示 -->
    <div class="current-path">
      <el-card>
        <div class="path-info">
          <span class="path-label">📁 当前路径：</span>
          <span class="path-value">{{ currentFolderPath }}</span>
        </div>
      </el-card>
    </div>

    <!-- 内容列表 - 按照现有系统的表格风格 -->
    <div class="content-list">
      <el-table
        :data="paginatedFileList"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <template #empty>
          <div style="padding: 40px; text-align: center;">
            <el-icon size="48" style="color: #c0c4cc; margin-bottom: 16px;">
              <Document />
            </el-icon>
            <p style="color: #909399; margin: 0;">暂无文件数据</p>
            <p style="color: #c0c4cc; font-size: 12px; margin: 8px 0 0 0;">
              请检查 H:\PublishSystem 目录是否存在或联系管理员
            </p>
          </div>
        </template>

        <el-table-column type="selection" width="55" />

        <el-table-column label="文件信息" width="450" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="file-info">
              <div class="file-content">
                <!-- 视频缩略图 -->
                <div
                  v-if="!row.is_directory && isVideoFile(row)"
                  class="video-thumbnail-container"
                  :data-file-path="row.path"
                >
                  <img
                    v-if="getVideoThumbnailUrl(row)"
                    :src="getVideoThumbnailUrl(row)"
                    :alt="row.name"
                    class="video-thumbnail-small"
                    :style="getThumbnailStyle(row)"
                    @error="handleThumbnailError"
                    @click="previewVideo(row)"
                  />
                  <div v-else class="video-thumbnail-placeholder" @click="previewVideo(row)">
                    <el-icon><VideoPlay /></el-icon>
                  </div>
                </div>

                <!-- 文件信息 -->
                <div class="file-details">
                  <div class="file-title">
                    <!-- 长视频特殊图标 -->
                    <el-icon v-if="isLongVideo(row)" style="margin-right: 4px; color: #ff9800;" title="长视频（超过35秒）">
                      <Clock />
                    </el-icon>
                    <el-icon v-else-if="row.is_directory" style="margin-right: 4px; color: #409eff;">
                      <Folder />
                    </el-icon>
                    <el-icon v-else-if="!isVideoFile(row)" style="margin-right: 4px; color: #67c23a;">
                      <Document />
                    </el-icon>
                    <el-icon v-else style="margin-right: 4px; color: #f56c6c;">
                      <VideoPlay />
                    </el-icon>
                    <span class="file-name" :class="{ 'long-video-name': isLongVideo(row) }">{{ row.name }}</span>
                    <el-tag
                      v-if="row.content_type"
                      :type="getContentTypeColor(row.content_type)"
                      size="small"
                      style="margin-left: 8px;"
                    >
                      {{ getContentTypeText(row.content_type) }}
                    </el-tag>
                    <!-- 长视频标记 -->
                    <el-tag
                      v-if="isLongVideo(row)"
                      type="warning"
                      size="small"
                      class="long-video-tag"
                      style="margin-left: 8px;"
                      title="视频时长超过35秒"
                    >
                      ⏰ 长视频
                    </el-tag>
                  </div>
                  <div class="file-path">{{ row.path }}</div>
                  <div class="file-meta">
                    <span v-if="row.core_service" class="core-service">🖥️ {{ row.core_service }}</span>
                    <span v-if="row.platform" class="platform">🌐 {{ getPlatformText(row.platform) }}</span>
                    <!-- 视频信息 -->
                    <span v-if="!row.is_directory && isVideoFile(row) && row.media_info" class="video-info">
                      🎬 {{ formatDuration(row.media_info.duration) }}
                      📐 {{ row.media_info.video?.width }}x{{ row.media_info.video?.height }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="大小/类型" width="200">
          <template #default="{ row }">
            <div class="size-type-info">
              <div v-if="!row.is_directory" class="file-size">📦 {{ formatFileSize(row.size) }}</div>
              <div v-else class="directory-indicator">📁 文件夹</div>
              <div v-if="getFileExtension(row)" class="file-format">📄 {{ getFileExtension(row).toUpperCase() }}</div>
              <div v-else-if="!row.is_directory" class="file-format">📄 未知格式</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="平台/服务" width="200">
          <template #default="{ row }">
            <div class="platform-service-info">
              <div v-if="row.platform" class="platform">
                <el-tag :type="getPlatformColor(row.platform)" size="small">
                  {{ getPlatformText(row.platform) }}
                </el-tag>
              </div>
              <div v-if="row.core_service" class="core-service">
                <el-tag type="info" size="small" style="margin-top: 4px;">
                  {{ row.core_service }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="修改时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.last_modified) }}
          </template>
        </el-table-column>

        <!-- MD5记录状态列 - 简洁版 -->
        <el-table-column label="MD5状态" width="120">
          <template #default="{ row }">
            <div v-if="!row.is_directory" class="md5-status-simple">
              <div v-if="row.md5_hash" class="has-md5">
                <!-- 记录状态 -->
                <div class="record-status">
                  <el-tag
                    :type="getFileMD5RecordStatus(row) ? 'success' : 'info'"
                    size="small"
                  >
                    {{ getFileMD5RecordStatus(row) ? '📝 已记录' : '📝 未记录' }}
                  </el-tag>
                </div>

                <!-- 发布统计 -->
                <div v-if="getFileMD5RecordStatus(row)" class="publish-summary">
                  <span class="publish-count">
                    {{ getFilePublishedPlatformCount(row) }}/{{ getFileTotalPlatformCount(row) }} 已发布
                  </span>
                </div>
              </div>

              <!-- 没有MD5 -->
              <div v-else class="no-md5-simple">
                <el-tag type="warning" size="small">🔍 无MD5</el-tag>
              </div>
            </div>

            <!-- 文件夹 -->
            <div v-else class="folder-simple">
              <el-tag type="info" size="small">📁 文件夹</el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column v-if="showAdvancedInfo" label="媒体信息" width="200">
          <template #default="{ row }">
            <div v-if="!row.is_directory && row.media_info" class="media-info">
              <div v-if="row.media_info.duration" class="duration">
                ⏱️ {{ formatDurationFromSeconds(row.media_info.duration) }}
              </div>
              <div v-if="row.media_info.video" class="video-info">
                📺 {{ getVideoResolution(row.media_info) }}
              </div>
              <div v-if="row.media_info.bit_rate" class="bitrate">
                📊 {{ getVideoBitrate(row.media_info) }}
              </div>
            </div>
            <div v-else-if="!row.is_directory && showAdvancedInfo" class="no-media-info">
              <span class="loading-text">分析中...</span>
            </div>
            <div v-else class="directory-placeholder">-</div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                v-if="row.is_directory"
                type="primary"
                size="small"
                @click="openDirectory(row)"
              >
                打开
              </el-button>
              <el-button
                v-else
                type="primary"
                size="small"
                @click="viewFileDetail(row)"
              >
                详情
              </el-button>

              <!-- MD5记录管理按钮 -->
              <el-button
                v-if="!row.is_directory && row.md5_hash"
                :type="getFileMD5RecordStatus(row) ? 'success' : 'warning'"
                size="small"
                @click="quickEditFileMD5Record(row)"
                :title="getFileMD5RecordStatus(row) ? '编辑MD5记录' : '创建MD5记录'"
              >
                {{ getFileMD5RecordStatus(row) ? '📝' : '➕' }}
              </el-button>

              <!-- 视频预览按钮 - 仅对视频文件显示 -->
              <el-button
                v-if="!row.is_directory && isVideoFile(row)"
                type="info"
                size="small"
                @click="previewVideo(row)"
                title="预览视频"
              >
                <el-icon><VideoPlay /></el-icon>
              </el-button>

              <!-- 去水印按钮 - 仅对视频文件显示 -->
              <el-button
                v-if="!row.is_directory && isVideoFile(row)"
                type="danger"
                size="small"
                @click="processVideoWatermark(row)"
                title="检测并清除视频水印"
              >
                🚫
              </el-button>


              <el-button
                type="danger"
                size="small"
                @click="deleteFile(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 - 按照现有系统风格 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="totalCount"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 创建下载任务对话框 -->
    <el-dialog v-model="showCreateTaskDialog" title="创建下载任务" width="600px">
      <el-form :model="taskForm" label-width="100px">
        <el-form-item label="任务名称">
          <el-input v-model="taskForm.name" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="目标平台">
          <el-select v-model="taskForm.platform" placeholder="请选择平台" style="width: 100%">
            <el-option label="YouTube" value="youtube" />
            <el-option label="TikTok" value="tiktok" />
            <el-option label="Instagram" value="instagram" />
          </el-select>
        </el-form-item>
        <el-form-item label="下载链接">
          <el-input
            v-model="taskForm.urls"
            type="textarea"
            :rows="4"
            placeholder="请输入下载链接，每行一个"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateTaskDialog = false">取消</el-button>
        <el-button type="primary" @click="createDownloadTask">创建任务</el-button>
      </template>
    </el-dialog>

    <!-- 分类管理对话框 -->
    <el-dialog v-model="showCategoryDialog" title="分类管理" width="500px">
      <el-form :model="categoryForm" label-width="80px">
        <el-form-item label="分类名称">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类描述">
          <el-input v-model="categoryForm.description" placeholder="请输入分类描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCategoryDialog = false">取消</el-button>
        <el-button type="primary" @click="createCategory">创建分类</el-button>
      </template>
    </el-dialog>

    <!-- 批量重命名对话框 -->
    <el-dialog v-model="showBatchRenameDialog" title="批量重命名文件" width="700px">
      <div class="batch-rename-container">
        <!-- 重命名模式选择 -->
        <div class="rename-mode-selection">
          <el-radio-group v-model="batchRenameForm.mode" @change="onRenameModeChange">
            <el-radio value="selected">重命名选中文件 ({{ selectedItems.filter(f => !f.is_directory).length }} 个)</el-radio>
            <el-radio value="all">重命名当前文件夹所有文件 ({{ fileList.filter(f => !f.is_directory).length }} 个)</el-radio>
          </el-radio-group>
        </div>

        <!-- 选中文件预览 -->
        <div v-if="batchRenameForm.mode === 'selected' && selectedItems.filter(f => !f.is_directory).length > 0" class="selected-files-preview">
          <el-card>
            <template #header>
              <span>将要重命名的文件 ({{ selectedItems.filter(f => !f.is_directory).length }} 个)</span>
            </template>
            <div class="file-list">
              <div v-for="(file, index) in selectedItems.filter(f => !f.is_directory)" :key="file.name" class="file-item">
                <span class="file-index">{{ index + 1 }}.</span>
                <span class="file-name">{{ file.name }}</span>
                <el-tag size="small" type="info">{{ getFileExtension(file) || '无扩展名' }}</el-tag>
              </div>
            </div>
          </el-card>
        </div>

        <div class="rename-instructions">
          <el-alert
            :title="batchRenameForm.mode === 'selected' ? '选中文件重命名说明' : '全部文件重命名说明'"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>1. 请在下方文本框中输入新的文件名，每行一个（不需要包含文件扩展名）</p>
              <p>2. 文件名的顺序将按照{{ batchRenameForm.mode === 'selected' ? '选中文件的顺序' : '当前文件列表的排序' }}进行匹配</p>
              <p>3. 文件名数量必须与要重命名的文件数量一致</p>
              <p>4. 系统会自动保留原文件的扩展名</p>
            </template>
          </el-alert>
        </div>

        <el-form :model="batchRenameForm" label-width="100px" style="margin-top: 20px;">
          <el-form-item label="新文件名列表">
            <div class="filename-input-container">
              <el-input
                v-model="batchRenameForm.fileNames"
                type="textarea"
                :rows="Math.min(15, Math.max(5, getTargetFileCount()))"
                :placeholder="getRenamePlaceholder()"
              />
              <div class="filename-actions">
                <el-button
                  @click="copyCurrentFilenames"
                  type="success"
                  size="small"
                  :disabled="getTargetFileCount() === 0"
                >
                  📋 复制当前文件名
                </el-button>
              </div>
            </div>
            <div class="form-tip">
              需要重命名的文件数量: {{ getTargetFileCount() }} 个
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showBatchRenameDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="executeBatchRename"
          :loading="batchRenameLoading"
          :disabled="getTargetFileCount() === 0"
        >
          执行重命名 ({{ getTargetFileCount() }} 个文件)
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量设置发布状态对话框 -->
    <el-dialog v-model="showBatchPublishStatusDialog" title="📤 批量设置发布状态" width="700px">
      <div class="batch-publish-container">
        <!-- 当前路径信息 -->
        <div class="path-info-card">
          <el-card>
            <template #header>
              <span>📁 当前路径信息</span>
            </template>
            <div class="path-details">
              <div class="path-item">
                <span class="label">路径：</span>
                <span class="value">{{ currentFolderPath }}</span>
              </div>
              <div class="path-item">
                <span class="label">检测到的平台：</span>
                <el-tag :type="getCurrentPathInfo().platform !== 'unknown' ? 'success' : 'warning'">
                  {{ getCurrentPathInfo().platform }}
                </el-tag>
              </div>
              <div class="path-item">
                <span class="label">检测到的账号：</span>
                <el-tag :type="getCurrentPathInfoSync().account !== '未识别账号' ? 'success' : 'warning'">
                  {{ getCurrentPathInfoSync().account }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 选中文件预览 -->
        <div class="selected-files-info">
          <el-card>
            <template #header>
              <span>📋 将要设置发布状态的文件 ({{ selectedItems.filter(f => !f.is_directory && f.md5_hash).length }} 个)</span>
            </template>
            <div class="file-list">
              <div
                v-for="(file, index) in selectedItems.filter(f => !f.is_directory && f.md5_hash)"
                :key="file.name"
                class="file-item"
              >
                <span class="file-index">{{ index + 1 }}.</span>
                <span class="file-name">{{ file.name }}</span>
                <el-tag size="small" type="info">{{ getFileExtension(file) || '无扩展名' }}</el-tag>
                <el-tag size="small" type="success" v-if="file.md5_hash">有MD5</el-tag>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 发布状态设置表单 -->
        <el-form :model="batchPublishStatusForm" label-width="100px" style="margin-top: 20px;">
          <el-form-item label="发布平台">
            <el-input
              v-model="batchPublishStatusForm.platform"
              placeholder="平台名称"
            />
            <div class="form-tip">
              已自动填入检测到的平台：{{ getCurrentPathInfo().platform }}
            </div>
          </el-form-item>

          <el-form-item label="发布账号">
            <el-input
              v-model="batchPublishStatusForm.account"
              placeholder="账号名称"
            />
            <div class="form-tip">
              已自动填入检测到的账号：{{ getCurrentPathInfo().account }}
            </div>
          </el-form-item>

          <el-form-item label="发布状态">
            <el-switch
              v-model="batchPublishStatusForm.is_published"
              active-text="已发布"
              inactive-text="未发布"
            />
          </el-form-item>

          <el-form-item label="发布日期" v-if="batchPublishStatusForm.is_published">
            <el-date-picker
              v-model="batchPublishStatusForm.publish_date"
              type="date"
              placeholder="选择发布日期"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="视频ID" v-if="batchPublishStatusForm.is_published">
            <el-input
              v-model="batchPublishStatusForm.video_id"
              placeholder="平台上的视频ID"
            />
          </el-form-item>

          <el-form-item label="视频链接" v-if="batchPublishStatusForm.is_published">
            <el-input
              v-model="batchPublishStatusForm.video_url"
              placeholder="视频链接URL"
            />
          </el-form-item>

          <el-form-item label="备注">
            <el-input
              v-model="batchPublishStatusForm.notes"
              type="textarea"
              :rows="3"
              placeholder="备注信息"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showBatchPublishStatusDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="executeBatchPublishStatus"
          :loading="batchPublishStatusLoading"
          :disabled="selectedItems.filter(f => !f.is_directory && f.md5_hash).length === 0"
        >
          批量设置 ({{ selectedItems.filter(f => !f.is_directory && f.md5_hash).length }} 个文件)
        </el-button>
      </template>
    </el-dialog>

    <!-- 归档已发布文件对话框 -->
    <el-dialog v-model="showArchivePublishedDialog" title="📦 一键归档已发布文件" width="600px">
      <div class="archive-published-container">
        <!-- 当前路径信息 -->
        <div class="path-info-card">
          <el-card>
            <template #header>
              <span>📁 当前路径信息</span>
            </template>
            <div class="path-details">
              <div class="path-item">
                <span class="label">路径：</span>
                <span class="value">{{ currentFolderPath }}</span>
              </div>
              <div class="path-item">
                <span class="label">检测到的平台：</span>
                <el-tag :type="getCurrentPathInfo().platform !== 'unknown' ? 'success' : 'warning'">
                  {{ getCurrentPathInfo().platform }}
                </el-tag>
              </div>
              <div class="path-item">
                <span class="label">检测到的账号：</span>
                <el-tag :type="getCurrentPathInfoSync().account !== '未识别账号' ? 'success' : 'warning'">
                  {{ getCurrentPathInfoSync().account }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 归档设置 -->
        <el-form :model="archivePublishedForm" label-width="120px" style="margin-top: 20px;">
          <el-form-item label="归档文件夹名称">
            <el-input
              v-model="archivePublishedForm.archiveFolderName"
              placeholder="归档文件夹名称"
            />
            <div class="form-tip">
              已发布的文件将被移动到此子文件夹中
            </div>
          </el-form-item>

          <el-form-item label="检查平台">
            <el-checkbox-group v-model="archivePublishedForm.platforms">
              <el-checkbox value="youtube">YouTube</el-checkbox>
              <el-checkbox value="tiktok">TikTok</el-checkbox>
              <el-checkbox value="douyin">抖音</el-checkbox>
              <el-checkbox value="instagram">Instagram</el-checkbox>
              <el-checkbox value="facebook">Facebook</el-checkbox>
              <el-checkbox value="kuaishou">快手</el-checkbox>
              <el-checkbox value="xiaohongshu">小红书</el-checkbox>
              <el-checkbox value="weibo">微博</el-checkbox>
            </el-checkbox-group>
            <div class="form-tip">
              如果不选择任何平台，将检查所有平台的发布状态
            </div>
          </el-form-item>
        </el-form>

        <!-- 操作说明 -->
        <el-alert
          title="操作说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>1. 系统会扫描当前文件夹中的所有文件</p>
            <p>2. 检查每个文件在指定平台的发布状态</p>
            <p>3. 将已发布的文件移动到归档文件夹中</p>
            <p>4. 如果归档文件夹不存在，会自动创建</p>
            <p>5. 如果归档文件夹中已有同名文件，会自动重命名</p>
          </template>
        </el-alert>
      </div>

      <template #footer>
        <el-button @click="showArchivePublishedDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="executeArchivePublished"
          :loading="archivePublishedLoading"
        >
          开始归档
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量合并视频对话框 -->
    <el-dialog v-model="showVideoMergeDialog" title="批量合并视频" width="700px">
      <div class="video-merge-container">
        <div class="merge-instructions">
          <el-alert
            title="功能说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>🎬 将当前文件夹下的所有视频文件智能合并成指定时长范围的视频</p>
              <p>✨ 支持随机转场特效，充分利用所有短视频素材</p>
              <p>📁 合并后的视频将保存到当前文件夹下的 output 目录</p>
              <p>⚡ 系统会根据视频时长自动分组，确保最佳的合并效果</p>
            </template>
          </el-alert>
        </div>

        <el-form :model="videoMergeForm" label-width="120px" style="margin-top: 20px;">
          <el-form-item label="目标时长范围">
            <div style="display: flex; align-items: center; gap: 10px;">
              <el-input-number
                v-model="videoMergeForm.targetDurationMin"
                :min="10"
                :max="600"
                :step="10"
                style="width: 120px;"
              />
              <span>秒 至</span>
              <el-input-number
                v-model="videoMergeForm.targetDurationMax"
                :min="30"
                :max="1200"
                :step="10"
                style="width: 120px;"
              />
              <span>秒</span>
            </div>
            <div class="form-tip">
              建议设置为 60-180 秒，适合大多数平台的视频时长要求
            </div>
          </el-form-item>

          <el-form-item label="转场特效">
            <el-switch
              v-model="videoMergeForm.enableTransitions"
              active-text="启用随机转场特效"
              inactive-text="简单拼接"
            />
            <div class="form-tip">
              启用转场特效会让视频更加流畅，但处理时间会稍长
            </div>
          </el-form-item>

          <el-form-item label="输出质量">
            <el-radio-group v-model="videoMergeForm.outputQuality">
              <el-radio value="high">高质量 (较慢)</el-radio>
              <el-radio value="medium">中等质量 (推荐)</el-radio>
              <el-radio value="low">快速处理 (较快)</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="每组最大视频数">
            <el-input-number
              v-model="videoMergeForm.maxVideosPerMerge"
              :min="2"
              :max="20"
              :step="1"
              style="width: 120px;"
            />
            <div class="form-tip">
              限制每个合并视频包含的原视频数量，避免单个视频过于复杂
            </div>
          </el-form-item>

          <el-form-item label="当前文件夹">
            <el-input :value="currentFolderPath" readonly />
            <div class="form-tip">
              视频文件数量: {{ getVideoFileCount() }} 个
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showVideoMergeDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="startVideoMerge"
          :loading="videoMergeLoading"
          :disabled="getVideoFileCount() === 0"
        >
          开始合并
        </el-button>
      </template>
    </el-dialog>

    <!-- 视频合并进度对话框 -->
    <el-dialog v-model="showVideoMergeProgressDialog" title="视频合并进度" width="600px" :close-on-click-modal="false">
      <div class="merge-progress-container">
        <div class="progress-info">
          <el-steps :active="getMergeProgressStep()" align-center style="margin-bottom: 20px;">
            <el-step title="扫描视频" icon="Search" />
            <el-step title="分组处理" icon="Setting" />
            <el-step title="合并视频" icon="VideoPlay" />
            <el-step title="完成" icon="Check" />
          </el-steps>

          <div class="current-status">
            <h4>{{ videoMergeProgress?.current_step || '准备中...' }}</h4>
            <el-progress
              :percentage="videoMergeProgress?.progress || 0"
              :status="getProgressStatus()"
              :stroke-width="8"
            />
          </div>

          <div class="progress-details" v-if="videoMergeProgress">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="任务状态">
                <el-tag :type="getStatusTagType(videoMergeProgress.status)">
                  {{ getStatusText(videoMergeProgress.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="总视频数">
                {{ videoMergeProgress.total_videos }}
              </el-descriptions-item>
              <el-descriptions-item label="已处理">
                {{ videoMergeProgress.processed_videos }}
              </el-descriptions-item>
              <el-descriptions-item label="输出文件">
                {{ videoMergeProgress.output_files.length }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <div class="output-files" v-if="videoMergeProgress?.output_files.length > 0">
            <h4>已生成的视频文件：</h4>
            <el-tag
              v-for="file in videoMergeProgress.output_files"
              :key="file"
              style="margin: 4px;"
              type="success"
            >
              {{ file }}
            </el-tag>
          </div>

          <div class="error-message" v-if="videoMergeProgress?.error_message">
            <el-alert
              :title="videoMergeProgress.error_message"
              type="error"
              :closable="false"
            />
          </div>
        </div>
      </div>

      <template #footer>
        <el-button
          v-if="videoMergeProgress?.status === 'processing'"
          @click="cancelVideoMerge"
          type="danger"
        >
          取消任务
        </el-button>
        <el-button
          v-if="videoMergeProgress?.status === 'completed'"
          @click="openOutputFolder"
          type="primary"
        >
          打开输出文件夹
        </el-button>
        <el-button @click="showVideoMergeProgressDialog = false">
          {{ videoMergeProgress?.status === 'completed' ? '关闭' : '后台运行' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 三拼视频对话框 -->
    <el-dialog v-model="showTripleVideoDialog" title="三拼视频制作" width="700px">
      <div class="triple-video-container">
        <div class="triple-instructions">
          <el-alert
            title="功能说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>📱 将文件夹中的竖版视频（9:16）拼合成横版视频（16:9）</p>
              <p>🎯 分为左中右三个区域，依次播放视频（1,2,3 → 4,5,6 → ...）</p>
              <p>⏸️ 当一个视频播放时，其他两个显示第一帧准备状态</p>
              <p>📁 输出视频将保存到当前文件夹下的 triple_output 目录</p>
            </template>
          </el-alert>
        </div>

        <el-form :model="tripleVideoForm" label-width="140px" style="margin-top: 20px;">
          <div class="form-info">
            <el-icon><InfoFilled /></el-icon>
            <span>将使用每个视频的实际时长进行播放，无需设置固定时长</span>
          </div>

          <el-form-item label="转场时长">
            <el-input-number
              v-model="tripleVideoForm.transitionDuration"
              :min="0.1"
              :max="2.0"
              :step="0.1"
              :precision="1"
              style="width: 120px;"
            />
            <span style="margin-left: 8px;">秒</span>
            <div class="form-tip">
              视频切换时的转场时长，建议设置为 0.5 秒
            </div>
          </el-form-item>

          <el-form-item label="输出质量">
            <el-radio-group v-model="tripleVideoForm.outputQuality">
              <el-radio value="high">高质量 (较慢)</el-radio>
              <el-radio value="medium">中等质量 (推荐)</el-radio>
              <el-radio value="low">快速处理 (较快)</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="生成预览">
            <el-switch
              v-model="tripleVideoForm.enablePreview"
              active-text="生成30秒预览视频"
              inactive-text="仅生成完整视频"
            />
            <div class="form-tip">
              预览视频可以快速查看效果，但会增加处理时间
            </div>
          </el-form-item>

          <el-form-item label="当前文件夹">
            <el-input :value="currentFolderPath" readonly />
            <div class="form-tip">
              竖版视频文件数量: {{ getVerticalVideoCount() }} 个
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showTripleVideoDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="startTripleVideo"
          :loading="tripleVideoLoading"
          :disabled="getVerticalVideoCount() < 3"
        >
          开始制作
        </el-button>
      </template>
    </el-dialog>

    <!-- 三拼视频进度对话框 -->
    <el-dialog v-model="showTripleVideoProgressDialog" title="三拼视频制作进度" width="600px" :close-on-click-modal="false">
      <div class="triple-progress-container">
        <div class="progress-info">
          <el-steps :active="getTripleProgressStep()" align-center style="margin-bottom: 20px;">
            <el-step title="扫描竖版视频" icon="Search" />
            <el-step title="创建三拼视频" icon="VideoPlay" />
            <el-step title="生成预览" icon="View" />
            <el-step title="完成" icon="Check" />
          </el-steps>

          <div class="current-status">
            <h4>{{ tripleVideoProgress?.current_step || '准备中...' }}</h4>
            <el-progress
              :percentage="tripleVideoProgress?.progress || 0"
              :status="getTripleProgressStatus()"
              :stroke-width="8"
            />
          </div>

          <div class="progress-details" v-if="tripleVideoProgress">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="任务状态">
                <el-tag :type="getStatusTagType(tripleVideoProgress.status)">
                  {{ getStatusText(tripleVideoProgress.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="竖版视频数">
                {{ tripleVideoProgress.total_videos }}
              </el-descriptions-item>
              <el-descriptions-item label="已处理">
                {{ tripleVideoProgress.processed_videos }}
              </el-descriptions-item>
              <el-descriptions-item label="输出文件">
                {{ tripleVideoProgress.output_files.length }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <div class="output-files" v-if="tripleVideoProgress?.output_files.length > 0">
            <h4>已生成的视频文件：</h4>
            <el-tag
              v-for="file in tripleVideoProgress.output_files"
              :key="file"
              style="margin: 4px;"
              type="success"
            >
              {{ file }}
            </el-tag>
          </div>

          <div class="preview-section" v-if="tripleVideoProgress?.preview_file">
            <h4>预览视频：</h4>
            <el-tag style="margin: 4px;" type="warning">
              {{ tripleVideoProgress.preview_file }}
            </el-tag>
          </div>

          <div class="error-message" v-if="tripleVideoProgress?.error_message">
            <el-alert
              :title="tripleVideoProgress.error_message"
              type="error"
              :closable="false"
            />
          </div>
        </div>
      </div>

      <template #footer>
        <el-button
          v-if="tripleVideoProgress?.status === 'processing'"
          @click="cancelTripleVideo"
          type="danger"
        >
          取消任务
        </el-button>
        <el-button
          v-if="tripleVideoProgress?.status === 'completed'"
          @click="openTripleOutputFolder"
          type="primary"
        >
          打开输出文件夹
        </el-button>
        <el-button @click="showTripleVideoProgressDialog = false">
          {{ tripleVideoProgress?.status === 'completed' ? '关闭' : '后台运行' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 视频去水印对话框 -->
    <el-dialog v-model="showWatermarkDialog" title="🚫 视频去水印处理" width="800px">
      <div class="watermark-container">
        <div class="watermark-instructions">
          <el-alert
            title="功能说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>🔍 智能检测视频中的水印并自动清除</p>
              <p>⚡ 支持单文件处理和批量处理模式</p>
              <p>🎯 支持自动检测、模板匹配、手动指定区域等多种模式</p>
              <p>📁 处理后的视频将保存到指定位置</p>
            </template>
          </el-alert>
        </div>

        <el-form :model="watermarkForm" label-width="120px" style="margin-top: 20px;">
          <el-form-item label="处理模式">
            <el-radio-group v-model="watermarkForm.processMode">
              <el-radio value="single">单文件处理</el-radio>
              <el-radio value="batch">批量处理</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 单文件处理 -->
          <div v-if="watermarkForm.processMode === 'single'">
            <el-form-item label="输入文件">
              <el-input v-model="watermarkForm.inputFile" readonly />
            </el-form-item>
            <el-form-item label="输出文件">
              <el-input v-model="watermarkForm.outputFile" placeholder="留空则自动生成" />
              <div class="form-tip">
                留空将自动生成文件名（原文件名_no_watermark.扩展名）
              </div>
            </el-form-item>
          </div>

          <!-- 批量处理 -->
          <div v-if="watermarkForm.processMode === 'batch'">
            <el-form-item label="输入文件夹">
              <el-input v-model="watermarkForm.inputFolder" readonly />
            </el-form-item>
            <el-form-item label="输出文件夹">
              <el-input v-model="watermarkForm.outputFolder" placeholder="留空则使用输入文件夹下的cleaned子目录" />
            </el-form-item>
            <el-form-item label="文件过滤">
              <el-checkbox-group v-model="watermarkForm.fileFilters">
                <el-checkbox label="*.mp4">MP4文件</el-checkbox>
                <el-checkbox label="*.avi">AVI文件</el-checkbox>
                <el-checkbox label="*.mov">MOV文件</el-checkbox>
                <el-checkbox label="*.mkv">MKV文件</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="递归处理">
              <el-switch
                v-model="watermarkForm.recursive"
                active-text="处理子文件夹"
                inactive-text="仅当前文件夹"
              />
            </el-form-item>
          </div>

          <el-form-item label="检测模式">
            <el-radio-group v-model="watermarkForm.detectionMode">
              <el-radio value="auto">自动检测</el-radio>
              <el-radio value="template">模板匹配</el-radio>
              <el-radio value="region">区域检测</el-radio>
            </el-radio-group>
            <div class="form-tip">
              自动检测：智能识别常见位置的水印<br>
              模板匹配：使用预设模板精确匹配<br>
              区域检测：在指定区域内检测水印
            </div>
          </el-form-item>

          <el-form-item v-if="watermarkForm.detectionMode === 'template'" label="水印模板">
            <el-select v-model="watermarkForm.templatePath" placeholder="选择水印模板">
              <el-option label="抖音水印" value="douyin_logo.png" />
              <el-option label="快手水印" value="kuaishou_logo.png" />
              <el-option label="小红书水印" value="xiaohongshu_logo.png" />
              <el-option label="微博水印" value="weibo_logo.png" />
              <el-option label="自定义模板" value="custom" />
            </el-select>
          </el-form-item>

          <el-form-item v-if="watermarkForm.detectionMode === 'region'" label="检测区域">
            <el-input
              v-model="watermarkForm.detectionRegion"
              placeholder="格式：x,y,width,height（像素）"
            />
            <div class="form-tip">
              例如：100,50,200,100 表示从(100,50)开始，宽200高100的区域
            </div>
          </el-form-item>

          <el-form-item label="检测敏感度">
            <el-slider
              v-model="watermarkForm.sensitivity"
              :min="0.1"
              :max="1.0"
              :step="0.1"
              show-stops
              show-input
              :format-tooltip="formatSensitivity"
            />
            <div class="form-tip">
              敏感度越高检测越严格，可能产生误报；敏感度越低可能遗漏水印
            </div>
          </el-form-item>

          <el-form-item label="清除模式">
            <el-radio-group v-model="watermarkForm.removalMode">
              <el-radio value="auto">自动清除</el-radio>
              <el-radio value="manual">手动指定</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="watermarkForm.removalMode === 'manual'" label="水印区域">
            <el-input
              v-model="watermarkForm.watermarkRegions"
              type="textarea"
              :rows="3"
              placeholder="每行一个区域，格式：x,y,width,height"
            />
            <div class="form-tip">
              例如：<br>
              100,50,200,100<br>
              300,400,150,80
            </div>
          </el-form-item>

          <el-form-item label="修复算法">
            <el-select v-model="watermarkForm.inpaintMethod">
              <el-option label="模糊处理" value="blur" />
              <el-option label="中值滤波" value="median" />
              <el-option label="图像修复" value="inpaint" />
            </el-select>
            <div class="form-tip">
              模糊处理：速度快，适合简单水印<br>
              中值滤波：效果中等，适合噪点水印<br>
              图像修复：效果最好，但速度较慢
            </div>
          </el-form-item>

          <el-form-item label="输出质量">
            <el-radio-group v-model="watermarkForm.outputQuality">
              <el-radio value="high">高质量 (较慢)</el-radio>
              <el-radio value="medium">中等质量 (推荐)</el-radio>
              <el-radio value="low">快速处理 (较快)</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="watermarkForm.processMode === 'batch'" label="最大并发数">
            <el-input-number
              v-model="watermarkForm.maxConcurrent"
              :min="1"
              :max="8"
              style="width: 120px;"
            />
            <div class="form-tip">
              同时处理的视频数量，建议根据电脑性能调整
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showWatermarkDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="startWatermarkProcessing"
          :loading="watermarkProcessing"
        >
          {{ watermarkForm.processMode === 'single' ? '开始处理' : '批量处理' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 去水印进度对话框 -->
    <el-dialog v-model="showWatermarkProgressDialog" title="去水印处理进度" width="600px" :close-on-click-modal="false">
      <div class="watermark-progress-container">
        <div class="progress-info">
          <el-steps :active="getWatermarkProgressStep()" align-center style="margin-bottom: 20px;">
            <el-step title="检测水印" icon="Search" />
            <el-step title="清除水印" icon="Delete" />
            <el-step title="完成处理" icon="Check" />
          </el-steps>

          <div class="current-status">
            <h4>{{ watermarkProgress?.current_step || '准备中...' }}</h4>
            <el-progress
              :percentage="watermarkProgress?.progress || 0"
              :status="getWatermarkProgressStatus()"
              :stroke-width="8"
            />
          </div>

          <div class="progress-details" v-if="watermarkProgress">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="任务状态">
                <el-tag :type="getStatusTagType(watermarkProgress.status)">
                  {{ getStatusText(watermarkProgress.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="总文件数">
                {{ watermarkProgress.total_files }}
              </el-descriptions-item>
              <el-descriptions-item label="已处理">
                {{ watermarkProgress.processed_files }}
              </el-descriptions-item>
              <el-descriptions-item label="成功处理">
                {{ watermarkProgress.successful_files }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <div class="processing-files" v-if="watermarkProgress?.results?.length > 0">
            <h4>处理结果：</h4>
            <div class="file-results">
              <div
                v-for="result in watermarkProgress.results"
                :key="result.file_path"
                class="file-result-item"
              >
                <el-tag
                  :type="result.status === 'success' ? 'success' : 'danger'"
                  style="margin: 2px;"
                >
                  {{ getFileName(result.file_path) }} - {{ result.status === 'success' ? '成功' : '失败' }}
                </el-tag>
              </div>
            </div>
          </div>

          <div class="error-message" v-if="watermarkProgress?.error">
            <el-alert
              :title="watermarkProgress.error"
              type="error"
              :closable="false"
            />
          </div>
        </div>
      </div>

      <template #footer>
        <el-button
          v-if="watermarkProgress?.status === 'processing'"
          @click="cancelWatermarkProcessing"
          type="danger"
        >
          取消任务
        </el-button>
        <el-button
          v-if="watermarkProgress?.status === 'completed'"
          @click="openWatermarkOutputFolder"
          type="primary"
        >
          打开输出文件夹
        </el-button>
        <el-button @click="showWatermarkProgressDialog = false">
          {{ watermarkProgress?.status === 'completed' ? '关闭' : '后台运行' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 视频旋转对话框 -->
    <el-dialog v-model="showVideoRotationDialog" title="视频旋转" width="600px">
      <div class="video-rotation-container">
        <!-- 选中文件预览 -->
        <div class="selected-files-info">
          <el-card>
            <template #header>
              <span>将要旋转的视频文件 ({{ selectedVideoFiles.length }} 个)</span>
            </template>
            <div class="file-list">
              <div v-for="(file, index) in selectedVideoFiles" :key="file.path" class="file-item">
                <span class="file-index">{{ index + 1 }}.</span>
                <span class="file-name">{{ file.name }}</span>
                <el-tag size="small" type="info">{{ formatFileSize(file.size) }}</el-tag>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 旋转设置 -->
        <el-form :model="videoRotationForm" label-width="120px" style="margin-top: 20px;">
          <el-form-item label="旋转角度">
            <el-radio-group v-model="videoRotationForm.rotationAngle">
              <el-radio :label="90">向右旋转90°（顺时针）</el-radio>
              <el-radio :label="-90">向左旋转90°（逆时针）</el-radio>
              <el-radio :label="180">旋转180°</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="输出质量">
            <el-select v-model="videoRotationForm.outputQuality" style="width: 200px;">
              <el-option label="高质量" value="high" />
              <el-option label="中等质量" value="medium" />
              <el-option label="低质量" value="low" />
            </el-select>
            <div class="form-tip">
              高质量：文件较大，处理时间较长；低质量：文件较小，处理速度较快
            </div>
          </el-form-item>

          <el-form-item label="输出方式">
            <el-radio-group v-model="videoRotationForm.overwriteOriginal">
              <el-radio :label="false">保留原文件，生成新文件</el-radio>
              <el-radio :label="true">覆盖原文件</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="!videoRotationForm.overwriteOriginal" label="文件名后缀">
            <el-input
              v-model="videoRotationForm.outputSuffix"
              style="width: 200px;"
              placeholder="_rotated"
            />
            <div class="form-tip">
              新文件名格式：原文件名 + 后缀 + 扩展名
            </div>
          </el-form-item>
        </el-form>

        <!-- 预计处理时间提示 -->
        <el-alert
          title="处理提示"
          type="info"
          :closable="false"
          style="margin-top: 20px;"
        >
          <template #default>
            <p>• 视频旋转需要重新编码，处理时间取决于视频大小和质量设置</p>
            <p>• 建议在处理大量视频时选择较低的输出质量以节省时间</p>
            <p>• 处理过程中请勿关闭浏览器</p>
          </template>
        </el-alert>
      </div>

      <template #footer>
        <el-button @click="showVideoRotationDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="executeVideoRotation"
          :loading="videoRotationLoading"
        >
          {{ videoRotationLoading ? '正在旋转...' : '开始旋转' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 视频加速对话框 -->
    <el-dialog v-model="showVideoAccelerationDialog" title="视频加速" width="600px">
      <div class="video-acceleration-container">
        <!-- 选中文件预览 -->
        <div class="selected-files-info">
          <el-card>
            <template #header>
              <span>已选择 {{ selectedVideoFiles.length }} 个视频文件</span>
            </template>
            <div class="file-list">
              <div v-for="file in selectedVideoFiles.slice(0, 5)" :key="file.name" class="file-item">
                <el-icon><VideoPlay /></el-icon>
                <span class="file-name">{{ file.name }}</span>
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
                <span v-if="file.media_info?.duration" class="file-duration">
                  {{ formatDurationFromSeconds(file.media_info.duration) }}
                </span>
              </div>
              <div v-if="selectedVideoFiles.length > 5" class="more-files">
                还有 {{ selectedVideoFiles.length - 5 }} 个文件...
              </div>
            </div>
          </el-card>
        </div>

        <!-- 加速参数设置 -->
        <el-form :model="videoAccelerationForm" label-width="120px" class="processing-form">
          <el-form-item label="目标时长">
            <el-input-number
              v-model="videoAccelerationForm.targetDuration"
              :min="1"
              :max="300"
              :step="1"
              controls-position="right"
              style="width: 200px"
            />
            <span style="margin-left: 10px; color: #909399;">秒（默认59秒）</span>
          </el-form-item>

          <el-form-item label="输出质量">
            <el-radio-group v-model="videoAccelerationForm.outputQuality">
              <el-radio label="high">高质量（较慢）</el-radio>
              <el-radio label="medium">中等质量（推荐）</el-radio>
              <el-radio label="low">低质量（较快）</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="输出选项">
            <el-checkbox v-model="videoAccelerationForm.overwriteOriginal">
              覆盖原文件
            </el-checkbox>
            <div v-if="!videoAccelerationForm.overwriteOriginal" style="margin-top: 10px;">
              <el-input
                v-model="videoAccelerationForm.outputSuffix"
                placeholder="输出文件后缀"
                style="width: 200px"
                prefix-icon="Edit"
              />
              <span style="margin-left: 10px; color: #909399;">
                例如：原文件.mp4 → 原文件_accelerated.mp4
              </span>
            </div>
          </el-form-item>
        </el-form>

        <!-- 说明信息 -->
        <el-alert
          title="视频加速说明"
          type="info"
          :closable="false"
          style="margin-top: 20px;"
        >
          <template #default>
            <p>• 视频加速会调整播放速度以达到指定时长，音频也会同步加速</p>
            <p>• 如果原视频时长已小于目标时长，则不会进行加速处理</p>
            <p>• 加速倍率最大限制为10倍，避免过度加速影响观看体验</p>
            <p>• 处理过程中请勿关闭浏览器</p>
          </template>
        </el-alert>
      </div>

      <template #footer>
        <el-button @click="showVideoAccelerationDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="executeVideoAcceleration"
          :loading="videoAccelerationLoading"
        >
          {{ videoAccelerationLoading ? '正在加速...' : '开始加速' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量生成字幕对话框 -->
    <el-dialog v-model="showGenerateSubtitlesDialog" title="📝 批量生成字幕" width="600px">
      <div class="audio-processing-container">
        <div class="processing-instructions">
          <el-alert
            title="批量生成字幕说明"
            type="info"
            :closable="false"
            show-icon
          >
            <p>• 支持视频和音频文件的字幕生成</p>
            <p>• 使用OpenAI Whisper模型进行语音识别</p>
            <p>• 生成的字幕文件将保存在 subtitles 子文件夹中</p>
            <p>• 支持多种输出格式：SRT、VTT、TXT</p>
          </el-alert>
        </div>

        <el-form :model="generateSubtitlesForm" label-width="120px" class="processing-form">
          <el-form-item label="输出格式">
            <el-select v-model="generateSubtitlesForm.outputFormat" placeholder="选择输出格式">
              <el-option label="SRT (推荐)" value="srt" />
              <el-option label="VTT" value="vtt" />
              <el-option label="TXT" value="txt" />
            </el-select>
          </el-form-item>

          <el-form-item label="语言">
            <el-select v-model="generateSubtitlesForm.language" placeholder="选择语言">
              <el-option label="自动检测" value="auto" />
              <el-option label="中文" value="zh" />
              <el-option label="英文" value="en" />
              <el-option label="日文" value="ja" />
              <el-option label="韩文" value="ko" />
            </el-select>
          </el-form-item>

          <el-form-item label="模型大小">
            <el-select v-model="generateSubtitlesForm.modelSize" placeholder="选择模型大小">
              <el-option label="Tiny (最快)" value="tiny" />
              <el-option label="Base (推荐)" value="base" />
              <el-option label="Small (平衡)" value="small" />
              <el-option label="Medium (高质量)" value="medium" />
              <el-option label="Large (最高质量)" value="large" />
            </el-select>
          </el-form-item>

          <el-form-item label="并发数">
            <el-input-number
              v-model="generateSubtitlesForm.maxConcurrent"
              :min="1"
              :max="4"
              placeholder="最大并发处理数"
            />
            <div class="form-tip">建议设置为1-2，避免系统负载过高</div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showGenerateSubtitlesDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="executeGenerateSubtitles"
          :loading="generateSubtitlesLoading"
        >
          {{ generateSubtitlesLoading ? '正在生成...' : '开始生成字幕' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量分离音频对话框 -->
    <el-dialog v-model="showExtractAudioDialog" title="🎵 批量分离音频" width="600px">
      <div class="audio-processing-container">
        <div class="processing-instructions">
          <el-alert
            title="批量分离音频说明"
            type="info"
            :closable="false"
            show-icon
          >
            <p>• 从视频文件中提取音频轨道</p>
            <p>• 支持多种输出格式：WAV、MP3、AAC</p>
            <p>• 分离的音频文件将保存在 extracted_audio 子文件夹中</p>
            <p>• 可选择不同的音频质量</p>
          </el-alert>
        </div>

        <el-form :model="extractAudioForm" label-width="120px" class="processing-form">
          <el-form-item label="输出格式">
            <el-select v-model="extractAudioForm.outputFormat" placeholder="选择输出格式">
              <el-option label="WAV (无损)" value="wav" />
              <el-option label="MP3 (压缩)" value="mp3" />
              <el-option label="AAC (高效)" value="aac" />
            </el-select>
          </el-form-item>

          <el-form-item label="音频质量">
            <el-select v-model="extractAudioForm.quality" placeholder="选择音频质量">
              <el-option label="高质量" value="high" />
              <el-option label="中等质量" value="medium" />
              <el-option label="低质量" value="low" />
            </el-select>
          </el-form-item>

          <el-form-item label="并发数">
            <el-input-number
              v-model="extractAudioForm.maxConcurrent"
              :min="1"
              :max="5"
              placeholder="最大并发处理数"
            />
            <div class="form-tip">建议设置为2-3，平衡速度和系统负载</div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showExtractAudioDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="executeExtractAudio"
          :loading="extractAudioLoading"
        >
          {{ extractAudioLoading ? '正在分离...' : '开始分离音频' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量人声分离对话框 -->
    <el-dialog v-model="showSeparateVocalsDialog" title="🎤 批量人声分离" width="600px">
      <div class="audio-processing-container">
        <div class="processing-instructions">
          <el-alert
            title="批量人声分离说明"
            type="info"
            :closable="false"
            show-icon
          >
            <p>• 将音频文件分离为人声和伴奏两部分</p>
            <p>• 支持FFmpeg和Librosa两种分离方法</p>
            <p>• 分离的文件将保存在 vocals_separated 子文件夹中</p>
            <p>• 生成 _vocals.wav (人声) 和 _instrumental.wav (伴奏) 文件</p>
          </el-alert>
        </div>

        <el-form :model="separateVocalsForm" label-width="120px" class="processing-form">
          <el-form-item label="分离方法">
            <el-select v-model="separateVocalsForm.separationMethod" placeholder="选择分离方法">
              <el-option label="FFmpeg (快速)" value="ffmpeg" />
              <el-option label="Librosa (高质量)" value="librosa" />
            </el-select>
          </el-form-item>

          <el-form-item label="输出质量">
            <el-select v-model="separateVocalsForm.outputQuality" placeholder="选择输出质量">
              <el-option label="高质量" value="high" />
              <el-option label="中等质量" value="medium" />
              <el-option label="低质量" value="low" />
            </el-select>
          </el-form-item>

          <el-form-item label="并发数">
            <el-input-number
              v-model="separateVocalsForm.maxConcurrent"
              :min="1"
              :max="3"
              placeholder="最大并发处理数"
            />
            <div class="form-tip">人声分离较耗时，建议设置为1-2</div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showSeparateVocalsDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="executeSeparateVocals"
          :loading="separateVocalsLoading"
        >
          {{ separateVocalsLoading ? '正在分离...' : '开始人声分离' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量替换音频对话框 -->
    <el-dialog v-model="showReplaceAudioDialog" title="🔄 批量替换音频" width="600px">
      <div class="audio-processing-container">
        <div class="processing-instructions">
          <el-alert
            title="批量替换音频说明"
            type="info"
            :closable="false"
            show-icon
          >
            <p>• 将视频文件的音频轨道替换为指定的音频文件</p>
            <p>• 支持音量调节和淡入淡出效果</p>
            <p>• 替换后的视频将保存在 audio_replaced 子文件夹中</p>
            <p>• 视频流保持不变，只替换音频轨道</p>
          </el-alert>
        </div>

        <el-form :model="replaceAudioForm" label-width="120px" class="processing-form">
          <el-form-item label="新音频文件" required>
            <el-input
              v-model="replaceAudioForm.newAudioPath"
              placeholder="请输入音频文件的完整路径"
              clearable
            />
            <div class="form-tip">请输入要替换的音频文件的完整路径</div>
          </el-form-item>

          <el-form-item label="音频音量">
            <el-slider
              v-model="replaceAudioForm.audioVolume"
              :min="0"
              :max="2"
              :step="0.1"
              show-input
              :format-tooltip="(val) => `${val}x`"
            />
            <div class="form-tip">1.0为原始音量，2.0为两倍音量</div>
          </el-form-item>

          <el-form-item label="淡入淡出">
            <el-input-number
              v-model="replaceAudioForm.fadeDuration"
              :min="0"
              :max="5"
              :step="0.1"
              placeholder="淡入淡出时长（秒）"
            />
            <div class="form-tip">设置音频开始和结束的淡入淡出时长</div>
          </el-form-item>

          <el-form-item label="并发数">
            <el-input-number
              v-model="replaceAudioForm.maxConcurrent"
              :min="1"
              :max="5"
              placeholder="最大并发处理数"
            />
            <div class="form-tip">建议设置为2-3，平衡速度和系统负载</div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showReplaceAudioDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="executeReplaceAudio"
          :loading="replaceAudioLoading"
          :disabled="!replaceAudioForm.newAudioPath"
        >
          {{ replaceAudioLoading ? '正在替换...' : '开始替换音频' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 对标账号下载对话框 -->
    <el-dialog v-model="showBenchmarkDownloadDialog" title="对标账号内容下载" width="900px">
      <div class="benchmark-download-container">
        <!-- 步骤指示器 -->
        <el-steps :active="downloadStep" align-center style="margin-bottom: 30px;">
          <el-step title="选择对标账号" icon="User" />
          <el-step title="配置下载参数" icon="Setting" />
          <el-step title="确认并开始下载" icon="Download" />
        </el-steps>

        <!-- 步骤1: 选择对标账号 -->
        <div v-if="downloadStep === 0" class="step-content">
          <div class="step-header">
            <h3>📋 选择要下载内容的对标账号</h3>
            <p>选择一个或多个对标账号来下载其最新内容</p>
          </div>

          <div class="benchmark-selection">
            <el-table
              :data="benchmarkAccounts"
              v-loading="loadingBenchmarks"
              @selection-change="handleBenchmarkSelection"
              style="width: 100%"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column label="账号信息" min-width="200">
                <template #default="{ row }">
                  <div class="account-info">
                    <div class="account-name">{{ row.account_name }}</div>
                    <div class="account-meta">
                      <el-tag size="small" type="info">{{ row.platform }}</el-tag>
                      <el-tag size="small" :type="getBenchmarkTypeColor(row.benchmark_type)" style="margin-left: 4px;">
                        {{ getBenchmarkTypeText(row.benchmark_type) }}
                      </el-tag>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="粉丝数" width="100">
                <template #default="{ row }">
                  <span v-if="row.account_data?.followers">
                    {{ formatNumber(row.account_data.followers) }}
                  </span>
                  <span v-else class="no-data">-</span>
                </template>
              </el-table-column>
              <el-table-column label="内容数" width="100">
                <template #default="{ row }">
                  <span v-if="row.account_data?.posts_count">
                    {{ formatNumber(row.account_data.posts_count) }}
                  </span>
                  <span v-else class="no-data">-</span>
                </template>
              </el-table-column>
              <el-table-column label="优先级" width="100">
                <template #default="{ row }">
                  <span class="priority-stars">
                    {{ '★'.repeat(row.priority) }}{{ '☆'.repeat(5 - row.priority) }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 步骤2: 配置下载参数 -->
        <div v-if="downloadStep === 1" class="step-content">
          <div class="step-header">
            <h3>⚙️ 配置下载参数</h3>
            <p>设置下载的内容类型、数量和保存路径</p>
          </div>

          <el-form :model="downloadConfig" label-width="120px" class="download-config-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="下载路径">
                  <el-input v-model="downloadConfig.basePath" readonly>
                    <template #prepend>基础路径</template>
                  </el-input>
                  <div class="path-preview">
                    路径结构: {{ getFullDownloadPath() }}
                  </div>
                  <div class="path-explanation">
                    <small>路径说明：基础路径/关联账号平台/关联账号名称/对标账号名称/发布月份/</small>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="内容类型">
                  <el-checkbox-group v-model="downloadConfig.contentTypes">
                    <el-checkbox label="video">视频</el-checkbox>
                    <el-checkbox label="image">图片</el-checkbox>
                    <el-checkbox label="audio">音频</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="下载数量">
                  <el-input-number
                    v-model="downloadConfig.maxCount"
                    :min="1"
                    :max="100"
                    style="width: 100%"
                  />
                  <div class="form-tip">每个账号最多下载的内容数量</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="时间范围">
                  <el-select v-model="downloadConfig.timeRange" style="width: 100%">
                    <el-option label="最近1周" value="1week" />
                    <el-option label="最近1个月" value="1month" />
                    <el-option label="最近3个月" value="3months" />
                    <el-option label="最近6个月" value="6months" />
                    <el-option label="全部" value="all" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="下载模式">
                  <el-radio-group v-model="downloadConfig.downloadMode">
                    <el-radio label="auto">自动下载</el-radio>
                    <el-radio label="manual">手动确认</el-radio>
                  </el-radio-group>
                  <div class="form-tip">自动下载会立即开始，手动确认需要逐个确认</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="文件命名">
                  <el-select v-model="downloadConfig.namingRule" style="width: 100%">
                    <el-option label="原始标题" value="original" />
                    <el-option label="时间戳_标题" value="timestamp" />
                    <el-option label="序号_标题" value="sequence" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="过滤条件">
              <el-row :gutter="10">
                <el-col :span="8">
                  <el-input
                    v-model="downloadConfig.minViews"
                    placeholder="最小播放量"
                    type="number"
                  >
                    <template #prepend>播放量 ≥</template>
                  </el-input>
                </el-col>
                <el-col :span="8">
                  <el-input
                    v-model="downloadConfig.minLikes"
                    placeholder="最小点赞数"
                    type="number"
                  >
                    <template #prepend>点赞数 ≥</template>
                  </el-input>
                </el-col>
                <el-col :span="8">
                  <el-input
                    v-model="downloadConfig.keywords"
                    placeholder="关键词过滤"
                  >
                    <template #prepend>包含关键词</template>
                  </el-input>
                </el-col>
              </el-row>
            </el-form-item>
          </el-form>
        </div>

        <!-- 步骤3: 确认并开始下载 -->
        <div v-if="downloadStep === 2" class="step-content">
          <div class="step-header">
            <h3>✅ 确认下载任务</h3>
            <p>请确认以下下载配置，点击开始下载后将创建下载任务</p>
          </div>

          <div class="download-summary">
            <el-card class="summary-card">
              <template #header>
                <span>📊 下载任务摘要</span>
              </template>

              <el-descriptions :column="2" border>
                <el-descriptions-item label="选中账号">
                  {{ selectedBenchmarkAccounts.length }} 个
                </el-descriptions-item>
                <el-descriptions-item label="内容类型">
                  {{ downloadConfig.contentTypes.join(', ') }}
                </el-descriptions-item>
                <el-descriptions-item label="每账号数量">
                  最多 {{ downloadConfig.maxCount }} 个
                </el-descriptions-item>
                <el-descriptions-item label="时间范围">
                  {{ getTimeRangeText(downloadConfig.timeRange) }}
                </el-descriptions-item>
                <el-descriptions-item label="下载模式">
                  {{ downloadConfig.downloadMode === 'auto' ? '自动下载' : '手动确认' }}
                </el-descriptions-item>
                <el-descriptions-item label="保存路径">
                  {{ downloadConfig.basePath }}
                </el-descriptions-item>
              </el-descriptions>

              <div class="selected-accounts" style="margin-top: 20px;">
                <h4>选中的对标账号:</h4>
                <div class="account-chips">
                  <el-tag
                    v-for="account in selectedBenchmarkAccounts"
                    :key="account._id"
                    size="large"
                    style="margin: 4px;"
                  >
                    {{ account.platform }} - {{ account.account_name }}
                  </el-tag>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showBenchmarkDownloadDialog = false">取消</el-button>
          <el-button v-if="downloadStep > 0" @click="downloadStep--">上一步</el-button>
          <el-button
            v-if="downloadStep < 2"
            type="primary"
            @click="nextDownloadStep"
            :disabled="!canProceedToNextStep"
          >
            下一步
          </el-button>
          <el-button
            v-if="downloadStep === 2"
            type="success"
            @click="startBenchmarkDownload"
            :loading="downloadingBenchmark"
          >
            开始下载
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 内容详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="内容详情" width="800px">
      <div v-if="selectedContent">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="标题">{{ selectedContent.title }}</el-descriptions-item>
          <el-descriptions-item label="平台">{{ selectedContent.platform }}</el-descriptions-item>
          <el-descriptions-item label="类型">{{ getContentTypeText(selectedContent.content_type) }}</el-descriptions-item>
          <el-descriptions-item label="作者">{{ selectedContent.author.name }}</el-descriptions-item>
          <el-descriptions-item label="文件大小">{{ formatFileSize(selectedContent.file_info.file_size) }}</el-descriptions-item>
          <el-descriptions-item label="文件格式">{{ selectedContent.file_info.file_format }}</el-descriptions-item>
          <el-descriptions-item label="描述" span="2">{{ selectedContent.description || '暂无描述' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- MD5记录管理对话框 -->
    <el-dialog v-model="showMD5ManagementDialog" title="🔐 视频MD5记录管理" width="1200px">
      <div class="md5-management-container">
        <!-- 操作工具栏 -->
        <div class="md5-toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="batchSaveMD5Records" :loading="md5Loading">
              💾 批量保存当前文件夹MD5
            </el-button>
            <el-button type="warning" @click="compareMD5Records" :loading="md5Loading">
              🔍 MD5比对检查
            </el-button>
            <el-button type="success" @click="refreshMD5Records">
              🔄 刷新记录
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="md5SearchQuery"
              placeholder="搜索文件名或MD5..."
              style="width: 200px"
              @input="searchMD5Records"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div>

        <!-- MD5记录列表 -->
        <div class="md5-records-list">
          <el-table
            :data="md5Records"
            v-loading="md5Loading"
            stripe
            style="width: 100%"
            @selection-change="handleMD5Selection"
          >
            <el-table-column type="selection" width="55" />

            <el-table-column label="文件信息" width="300" show-overflow-tooltip>
              <template #default="{ row }">
                <div class="file-info">
                  <div class="file-name">{{ row.file_name }}</div>
                  <div class="file-path">{{ row.file_path }}</div>
                  <div class="file-meta">
                    <span class="file-size">📦 {{ formatFileSize(row.file_size) }}</span>
                    <span v-if="row.duration" class="duration">⏱️ {{ formatDurationFromSeconds(row.duration) }}</span>
                    <span v-if="row.resolution" class="resolution">📺 {{ row.resolution }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="MD5哈希值" width="200">
              <template #default="{ row }">
                <div class="md5-info">
                  <span class="md5-value">{{ row.md5_hash.substring(0, 16) }}...</span>
                  <el-button
                    size="small"
                    text
                    @click="copyToClipboard(row.md5_hash)"
                    title="复制完整MD5"
                  >
                    📋
                  </el-button>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="发布状态" width="200">
              <template #default="{ row }">
                <div class="platform-publish-status">
                  <div v-if="row.platform_records && row.platform_records.length > 0" class="platform-list">
                    <div
                      v-for="platform in row.platform_records"
                      :key="platform.platform"
                      class="platform-item"
                    >
                      <el-tag
                        :type="platform.is_published ? 'success' : 'info'"
                        size="small"
                        class="platform-tag"
                      >
                        {{ getMD5PlatformDisplayName(platform.platform) }}
                        {{ platform.is_published ? '✓' : '✗' }}
                      </el-tag>
                    </div>
                  </div>
                  <div v-else class="no-platform-records">
                    <el-tag type="info" size="small">未设置平台</el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="发布统计" width="120">
              <template #default="{ row }">
                <div class="publish-stats">
                  <div class="stats-summary">
                    <span class="published-count">
                      {{ getPublishedPlatformCount(row.platform_records) }}
                    </span>
                    /
                    <span class="total-count">
                      {{ row.platform_records ? row.platform_records.length : 0 }}
                    </span>
                  </div>
                  <div class="stats-label">已发布/总数</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="备注" width="150" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.notes || '-' }}
              </template>
            </el-table-column>

            <el-table-column label="创建时间" width="120">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>

            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button
                    size="small"
                    @click="editMD5Record(row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="deleteMD5Record(row)"
                  >
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="md5-pagination">
          <el-pagination
            v-model:current-page="md5CurrentPage"
            v-model:page-size="md5PageSize"
            :total="md5TotalCount"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="loadMD5Records"
            @size-change="handleMD5SizeChange"
          />
        </div>
      </div>

      <template #footer>
        <el-button @click="showMD5ManagementDialog = false">关闭</el-button>
        <el-button
          v-if="selectedMD5Records.length > 0"
          type="danger"
          @click="batchDeleteMD5Records"
          :loading="md5Loading"
        >
          批量删除选中记录
        </el-button>
      </template>
    </el-dialog>

    <!-- MD5记录编辑对话框 -->
    <el-dialog v-model="showMD5EditDialog" title="编辑MD5记录 - 多平台发布管理" width="800px">
      <el-form :model="md5EditForm" label-width="100px">
        <el-form-item label="文件名">
          <el-input v-model="md5EditForm.file_name" readonly />
        </el-form-item>
        <el-form-item label="MD5哈希值">
          <el-input v-model="md5EditForm.md5_hash" readonly />
        </el-form-item>

        <!-- 平台发布状态管理 -->
        <el-form-item label="平台发布状态">
          <div class="platform-management">
            <div class="platform-header">
              <span>管理各平台的发布状态</span>
              <el-button size="small" @click="addNewPlatform">+ 添加平台</el-button>
            </div>

            <div class="platform-list">
              <div
                v-for="(platform, index) in md5EditForm.platform_records"
                :key="index"
                class="platform-record"
              >
                <el-card class="platform-card">
                  <div class="platform-content">
                    <div class="platform-basic">
                      <el-select
                        v-model="platform.platform"
                        placeholder="选择平台"
                        style="width: 150px;"
                      >
                        <el-option label="YouTube" value="youtube" />
                        <el-option label="TikTok" value="tiktok" />
                        <el-option label="Instagram" value="instagram" />
                        <el-option label="微博" value="weibo" />
                        <el-option label="抖音" value="douyin" />
                        <el-option label="快手" value="kuaishou" />
                        <el-option label="小红书" value="xiaohongshu" />
                        <el-option label="B站" value="bilibili" />
                        <el-option label="其他" value="other" />
                      </el-select>

                      <el-switch
                        v-model="platform.is_published"
                        active-text="已发布"
                        inactive-text="未发布"
                        style="margin-left: 20px;"
                      />

                      <el-button
                        type="danger"
                        size="small"
                        @click="removePlatform(index)"
                        style="margin-left: 20px;"
                      >
                        删除
                      </el-button>
                    </div>

                    <div v-if="platform.is_published" class="platform-details">
                      <el-row :gutter="20">
                        <el-col :span="8">
                          <el-form-item label="发布日期" label-width="80px">
                            <el-date-picker
                              v-model="platform.publish_date"
                              type="date"
                              placeholder="选择发布日期"
                              style="width: 100%"
                            />
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="发布账号" label-width="80px">
                            <el-input
                              v-model="platform.publish_account"
                              placeholder="输入账号名称"
                            />
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="视频ID" label-width="80px">
                            <el-input
                              v-model="platform.video_id"
                              placeholder="平台视频ID"
                            />
                          </el-form-item>
                        </el-col>
                      </el-row>

                      <el-row :gutter="20">
                        <el-col :span="12">
                          <el-form-item label="视频链接" label-width="80px">
                            <el-input
                              v-model="platform.video_url"
                              placeholder="视频链接URL"
                            />
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="备注" label-width="80px">
                            <el-input
                              v-model="platform.notes"
                              placeholder="平台备注信息"
                            />
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </div>
                  </div>
                </el-card>
              </div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="全局备注">
          <el-input
            v-model="md5EditForm.notes"
            type="textarea"
            :rows="3"
            placeholder="输入全局备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showMD5EditDialog = false">取消</el-button>
        <el-button type="primary" @click="saveMD5Record" :loading="md5Loading">保存</el-button>
      </template>
    </el-dialog>

    <!-- MD5比对结果对话框 -->
    <el-dialog v-model="showMD5CompareDialog" title="🔍 MD5比对结果" width="1000px">
      <div class="md5-compare-container">
        <div class="compare-summary">
          <el-alert
            :title="`比对完成：共检查 ${md5CompareResults.total_files} 个文件，发现 ${md5CompareResults.duplicate_count} 个重复文件`"
            :type="md5CompareResults.duplicate_count > 0 ? 'warning' : 'success'"
            :closable="false"
            show-icon
          />
        </div>

        <div class="compare-results" style="margin-top: 20px;">
          <el-table
            :data="md5CompareResults.results"
            stripe
            style="width: 100%"
            @selection-change="handleCompareSelection"
          >
            <el-table-column type="selection" width="55" />

            <el-table-column label="文件名" width="250" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.file_name }}
              </template>
            </el-table-column>

            <el-table-column label="MD5哈希值" width="200">
              <template #default="{ row }">
                <span class="md5-value">{{ row.md5_hash.substring(0, 16) }}...</span>
                <el-button
                  size="small"
                  text
                  @click="copyToClipboard(row.md5_hash)"
                  title="复制完整MD5"
                >
                  📋
                </el-button>
              </template>
            </el-table-column>

            <el-table-column label="重复状态" width="120">
              <template #default="{ row }">
                <el-tag
                  :type="row.is_duplicate ? 'danger' : 'success'"
                  size="small"
                >
                  {{ row.is_duplicate ? '重复' : '唯一' }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="已有记录信息" min-width="200">
              <template #default="{ row }">
                <div v-if="row.is_duplicate && row.existing_record" class="existing-record">
                  <div class="record-info">
                    <span class="record-name">{{ row.existing_record.file_name }}</span>
                  </div>
                  <div class="record-meta">
                    <el-tag
                      :type="row.existing_record.is_published ? 'success' : 'info'"
                      size="small"
                    >
                      {{ row.existing_record.is_published ? '已发布' : '未发布' }}
                    </el-tag>
                    <span v-if="row.existing_record.publish_platform" class="platform">
                      {{ row.existing_record.publish_platform }}
                    </span>
                  </div>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>

            <el-table-column label="文件路径" min-width="300" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.file_path }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <el-button @click="showMD5CompareDialog = false">关闭</el-button>
        <el-button
          v-if="selectedCompareResults.length > 0"
          type="danger"
          @click="batchDeleteDuplicateFiles"
          :loading="md5Loading"
        >
          一键删除选中的重复文件 ({{ selectedCompareResults.length }})
        </el-button>
      </template>
    </el-dialog>

    <!-- 视频预览对话框 -->
    <VideoPreviewDialog
      v-model="showVideoPreviewDialog"
      :video-info="selectedVideoForPreview"
    />

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import {
  Download,
  Refresh,
  FolderAdd,
  Search,
  TrendCharts,
  User,
  Setting,
  Folder,
  Document,
  VideoPlay,
  Clock
} from '@element-plus/icons-vue'
import { getCoreServices } from '@/api/core'
import {
  listFolderContents,
  listPlatformPaths,
  batchRenameFiles,
  archivePublishedFiles,
  getFilenamesForCopy,
  createVideoMergeTask,
  getVideoMergeProgress,
  cancelVideoMergeTask,
  type BatchRenameRequest,
  type VideoMergeRequest,
  type VideoMergeProgress,
  // 三拼视频相关API
  createTripleVideoTask,
  getTripleVideoProgress,
  cancelTripleVideoTask,
  type TripleVideoMergeRequest,
  type TripleVideoMergeProgress,
  // MD5记录管理相关API
  getVideoMD5Records,
  updateVideoMD5Record,
  batchSaveFolderMD5Records,
  compareFolderMD5Records,
  batchDeleteFiles,
  deleteFile as deleteFileAPI,
  deleteVideoMD5Record,
  updatePlatformPublishStatus,
  createVideoMD5Record,
  batchGetMD5Records,
  getPlatformPublishStatus,
  getAllPlatformPublishStatus,
  type VideoMD5Record,
  type VideoMD5RecordUpdate,
  type VideoMD5RecordCreate,
  type PlatformPublishRecord,
  type PlatformPublishUpdate,
  type MD5CompareResponse,
  type MD5CompareResult,
  // 账号相关API
  getAccounts,
  getDeviceAccounts,
  // 音频处理相关API
  generateSubtitlesBatch,
  extractAudioBatch,
  separateVocalsBatch,
  replaceAudioBatch,
  type GenerateSubtitlesRequest,
  type ExtractAudioRequest,
  type SeparateVocalsRequest,
  type ReplaceAudioRequest,
  type AudioProcessingResponse,
  type VocalSeparationResponse,
  type ReplaceAudioResponse
} from '@/api/social'

// 导入组件
import VideoPreviewDialog from './components/VideoPreviewDialog.vue'

// 定义数据类型
interface FileItem {
  name: string
  path: string
  size: number
  is_directory: boolean
  extension?: string
  last_modified: string
  platform?: string
  core_service?: string
  content_type?: string
  md5_hash?: string
  is_uploaded?: boolean
  media_info?: {
    duration?: number
    size?: number
    bit_rate?: number
    format_name?: string
    video?: {
      codec: string
      width: number
      height: number
      fps: number
    }
    audio?: {
      codec: string
      sample_rate: number
      channels: number
    }
  }
}

interface CoreService {
  id: string
  name: string
  status: string
  file_root_path?: string
}

interface CategoryTreeNode {
  id: string
  name: string
  content_count: number
  children: CategoryTreeNode[]
}

interface BenchmarkAccount {
  _id: string
  account_name: string
  platform: string
  benchmark_type: string
  priority: number
  account_data?: {
    followers?: number
    posts_count?: number
  }
}

interface DownloadConfig {
  basePath: string
  contentTypes: string[]
  maxCount: number
  timeRange: string
  downloadMode: string
  namingRule: string
  minViews: string
  minLikes: string
  keywords: string
}

// 响应式数据
const loading = ref(false)
const fileList = ref<FileItem[]>([])
const coreServices = ref<CoreService[]>([])
const selectedCoreService = ref('')

const categoryTree = ref<CategoryTreeNode[]>([
  { id: '1', name: '技术分析', content_count: 15, children: [] },
  { id: '2', name: '设计教程', content_count: 8, children: [] },
  { id: '3', name: '商业课程', content_count: 12, children: [] },
  { id: '4', name: '行业报告', content_count: 6, children: [] }
])

const stats = ref({
  total_count: 0,
  recent_count: 0,
  platform_stats: []
})

// 分页数据
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(41)

// 过滤和搜索
const searchQuery = ref('')
const filterPlatform = ref('')
const filterContentType = ref('')
const selectedItems = ref<FileItem[]>([])
const selectedContent = ref<any>(null)

// 对话框状态
const showCreateTaskDialog = ref(false)
const showCategoryDialog = ref(false)
const showDetailDialog = ref(false)
const showBenchmarkDownloadDialog = ref(false)
const showBatchRenameDialog = ref(false)
const showMD5ManagementDialog = ref(false)
const showMD5EditDialog = ref(false)
const showMD5CompareDialog = ref(false)
const showWatermarkDialog = ref(false)
const showWatermarkProgressDialog = ref(false)
const showBatchPublishStatusDialog = ref(false)
const showArchivePublishedDialog = ref(false)
const showVideoRotationDialog = ref(false)
const showVideoAccelerationDialog = ref(false)
const showVideoPreviewDialog = ref(false)

// 音频处理对话框状态
const showGenerateSubtitlesDialog = ref(false)
const showExtractAudioDialog = ref(false)
const showSeparateVocalsDialog = ref(false)
const showReplaceAudioDialog = ref(false)

// 文件管理增强功能状态
const currentFolderPath = ref('H:\\PublishSystem')
const showAdvancedInfo = ref(false)  // 是否显示MD5和媒体信息
const batchRenameForm = reactive({
  fileNames: '',  // 用户输入的文件名列表（每行一个）
  mode: 'selected'  // 重命名模式：'selected' 或 'all'
})
const batchRenameLoading = ref(false)

// 批量设置发布状态表单
const batchPublishStatusForm = reactive({
  platform: '',
  account: '',
  is_published: false,
  publish_date: '',
  video_id: '',
  video_url: '',
  notes: ''
})
const batchPublishStatusLoading = ref(false)

// 归档已发布文件表单
const archivePublishedForm = reactive({
  archiveFolderName: '已发布',
  platforms: [] as string[]
})
const archivePublishedLoading = ref(false)

// 视频合并相关
const showVideoMergeDialog = ref(false)
const showVideoMergeProgressDialog = ref(false)
const videoMergeLoading = ref(false)
const videoMergeProgress = ref<VideoMergeProgress | null>(null)
const videoMergeTaskId = ref<string>('')
const videoMergeForm = reactive({
  targetDurationMin: 60,
  targetDurationMax: 180,
  enableTransitions: true,
  outputQuality: 'medium' as 'high' | 'medium' | 'low',
  maxVideosPerMerge: 8
})

// 三拼视频相关数据
const showTripleVideoDialog = ref(false)
const showTripleVideoProgressDialog = ref(false)
const tripleVideoLoading = ref(false)
const tripleVideoTaskId = ref<string>('')
const tripleVideoProgress = ref<TripleVideoMergeProgress | null>(null)
const tripleVideoForm = reactive({
  videoDurationPerSegment: 10,
  transitionDuration: 0.5,
  outputQuality: 'medium' as 'high' | 'medium' | 'low',
  enablePreview: true
})

// 视频旋转相关数据
const videoRotationForm = reactive({
  rotationAngle: 90 as 90 | -90 | 180,
  outputQuality: 'medium' as 'high' | 'medium' | 'low',
  overwriteOriginal: false,
  outputSuffix: '_rotated'
})
const videoRotationLoading = ref(false)

// 视频加速相关数据
const videoAccelerationForm = reactive({
  targetDuration: 59,
  outputQuality: 'medium' as 'high' | 'medium' | 'low',
  overwriteOriginal: false,
  outputSuffix: '_accelerated'
})
const videoAccelerationLoading = ref(false)

// 音频处理相关数据
const generateSubtitlesForm = reactive({
  outputFormat: 'srt' as 'srt' | 'vtt' | 'txt',
  language: 'auto',
  modelSize: 'base' as 'tiny' | 'base' | 'small' | 'medium' | 'large',
  maxConcurrent: 2
})
const generateSubtitlesLoading = ref(false)

const extractAudioForm = reactive({
  outputFormat: 'wav' as 'wav' | 'mp3' | 'aac',
  quality: 'high' as 'high' | 'medium' | 'low',
  maxConcurrent: 3
})
const extractAudioLoading = ref(false)

const separateVocalsForm = reactive({
  separationMethod: 'ffmpeg' as 'ffmpeg' | 'librosa',
  outputQuality: 'high' as 'high' | 'medium' | 'low',
  maxConcurrent: 2
})
const separateVocalsLoading = ref(false)

const replaceAudioForm = reactive({
  newAudioPath: '',
  audioVolume: 1.0,
  fadeDuration: 0.5,
  maxConcurrent: 3
})
const replaceAudioLoading = ref(false)

// 视频预览相关数据
const selectedVideoForPreview = ref<FileItem | null>(null)

// 对标账号下载相关数据
const downloadStep = ref(0)
const benchmarkAccounts = ref<BenchmarkAccount[]>([])
const selectedBenchmarkAccounts = ref<BenchmarkAccount[]>([])
const loadingBenchmarks = ref(false)
const downloadingBenchmark = ref(false)

// 表单数据
const taskForm = reactive({
  name: '',
  platform: '',
  urls: ''
})

const categoryForm = reactive({
  name: '',
  description: ''
})

const downloadConfig = reactive<DownloadConfig>({
  basePath: 'H:\\PublishSystem\\',
  contentTypes: ['video'],
  maxCount: 10,
  timeRange: '1month',
  downloadMode: 'auto',
  namingRule: 'timestamp',
  minViews: '',
  minLikes: '',
  keywords: ''
})

// MD5记录管理相关数据
const md5Records = ref<VideoMD5Record[]>([])
const selectedMD5Records = ref<VideoMD5Record[]>([])
const md5Loading = ref(false)
const md5SearchQuery = ref('')
const md5CurrentPage = ref(1)
const md5PageSize = ref(20)
const md5TotalCount = ref(0)

// 文件MD5记录缓存 - 用于在文件列表中显示MD5记录状态
const fileMD5RecordsCache = ref<Map<string, VideoMD5Record>>(new Map())

// 账号信息缓存 - 用于显示真实的关联账号名称
const accountInfoCache = ref<Map<string, any>>(new Map())

// MD5记录编辑表单
const md5EditForm = reactive({
  file_name: '',
  md5_hash: '',
  platform_records: [] as PlatformPublishRecord[],
  notes: ''
})

// MD5比对结果
const md5CompareResults = ref<MD5CompareResponse>({
  results: [],
  total_files: 0,
  duplicate_count: 0
})
const selectedCompareResults = ref<MD5CompareResult[]>([])

// 去水印相关数据
const watermarkProcessing = ref(false)
const watermarkProgress = ref<any>(null)
const watermarkTaskId = ref<string>('')
const watermarkForm = reactive({
  processMode: 'single' as 'single' | 'batch',
  inputFile: '',
  outputFile: '',
  inputFolder: '',
  outputFolder: '',
  fileFilters: ['*.mp4'],
  recursive: false,
  detectionMode: 'auto' as 'auto' | 'template' | 'region',
  templatePath: '',
  detectionRegion: '',
  sensitivity: 0.7,
  removalMode: 'auto' as 'auto' | 'manual',
  watermarkRegions: '',
  inpaintMethod: 'blur' as 'blur' | 'median' | 'inpaint',
  outputQuality: 'medium' as 'high' | 'medium' | 'low',
  maxConcurrent: 3
})

// 计算属性
// 分页处理的文件列表
const paginatedFileList = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return fileList.value.slice(start, end)
})

const downloadingCount = computed(() => {
  // 对于文件列表，我们可以计算正在下载的文件数量
  // 这里暂时返回0，后续可以根据实际需求调整
  return 0
})

// 选中的视频文件
const selectedVideoFiles = computed(() => {
  const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v', 'webm', '3gp', 'ts']
  return selectedItems.value.filter(item => {
    if (item.is_directory) return false
    const extension = item.name.split('.').pop()?.toLowerCase()
    return extension && videoExtensions.includes(extension)
  })
})

const canProceedToNextStep = computed(() => {
  if (downloadStep.value === 0) {
    return selectedBenchmarkAccounts.value.length > 0
  }
  if (downloadStep.value === 1) {
    return downloadConfig.contentTypes.length > 0 && downloadConfig.maxCount > 0
  }
  return true
})

// 工具方法
const getTypeCount = (type: string) => {
  return fileList.value.filter(item => item.content_type === type).length
}

const getContentTypeText = (type: string) => {
  const typeMap = {
    video: '视频',
    image: '图片',
    audio: '音频',
    text: '文本'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const getContentTypeColor = (type: string) => {
  const colorMap = {
    video: 'primary',
    image: 'success',
    audio: 'warning',
    text: 'info'
  }
  return colorMap[type as keyof typeof colorMap] || 'info'
}

const getDownloadStatusText = (status: string) => {
  const statusMap = {
    downloaded: '已下载',
    processing: '下载中',
    failed: '失败'
  }
  return statusMap[status as keyof typeof statusMap] || status
}

const getDownloadStatusColor = (status: string) => {
  const colorMap = {
    downloaded: 'success',
    processing: 'primary',
    failed: 'danger'
  }
  return colorMap[status as keyof typeof colorMap] || 'info'
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatTime = (timeString: string) => {
  return new Date(timeString).toLocaleString('zh-CN')
}

// 对标账号相关工具方法
const getBenchmarkTypeText = (type: string) => {
  const typeMap = {
    original: '原创',
    recreate: '二创',
    repost: '搬运'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const getBenchmarkTypeColor = (type: string) => {
  const colorMap = {
    original: 'success',
    recreate: 'warning',
    repost: 'info'
  }
  return colorMap[type as keyof typeof colorMap] || 'info'
}

const getTimeRangeText = (range: string) => {
  const rangeMap = {
    '1week': '最近1周',
    '1month': '最近1个月',
    '3months': '最近3个月',
    '6months': '最近6个月',
    'all': '全部'
  }
  return rangeMap[range as keyof typeof rangeMap] || range
}

const getPlatformDisplayName = (platformId: string) => {
  // 根据数据库中的平台ObjectId映射到显示名称
  const platformIdMap = {
    '681efeeecd836bd64b9c2a1e': 'YouTube', // 油管
    '681efeeecd836bd64b9c2a20': 'TikTok',  // TT
    '681efeeecd836bd64b9c2a22': '抖音',     // 抖音
    '681efeeecd836bd64b9c2a24': 'Instagram', // IG
    '681efeeecd836bd64b9c2a26': 'Facebook', // FB
    '681efeeecd836bd64b9c2a28': 'Twitter',  // 推特
    '681efeeecd836bd64b9c2a30': 'LinkedIn', // 领英
    '681efeeecd836bd64b9c2a32': 'Snapchat', // 快拍
    '681efeeecd836bd64b9c2a34': 'Pinterest', // 拼趣
    '681efeeecd836bd64b9c2a36': 'Reddit',   // 红迪
    '681efeeecd836bd64b9c2a38': 'Tumblr',   // 汤博乐
    '681efeeecd836bd64b9c2a40': 'WeChat',   // 微信
    '681efeeecd836bd64b9c2a42': 'Weibo',    // 微博
    '681efeeecd836bd64b9c2a44': 'Bilibili', // B站
    '681efeeecd836bd64b9c2a46': 'Xiaohongshu' // 小红书
  }

  // 如果是ObjectId格式，直接查找映射
  if (platformIdMap[platformId as keyof typeof platformIdMap]) {
    return platformIdMap[platformId as keyof typeof platformIdMap]
  }

  // 如果是平台名称，直接返回
  const platformMap = {
    'youtube': 'YouTube',
    'tiktok': 'TikTok',
    'douyin': '抖音',
    'instagram': 'Instagram',
    'facebook': 'Facebook',
    'twitter': 'Twitter',
    'linkedin': 'LinkedIn',
    'snapchat': 'Snapchat',
    'pinterest': 'Pinterest',
    'reddit': 'Reddit',
    'tumblr': 'Tumblr',
    'wechat': '微信',
    'weibo': '微博',
    'bilibili': 'B站',
    'xiaohongshu': '小红书'
  }

  return platformMap[platformId as keyof typeof platformMap] || platformId
}

const formatOurAccountName = (accountInfo: any) => {
  if (!accountInfo) return '未知账号'

  // 获取账号名称，优先使用display_name，然后是username的前缀部分
  let name = accountInfo.display_name
  if (!name && accountInfo.username) {
    // 如果username是邮箱格式，取@前面的部分
    if (accountInfo.username.includes('@')) {
      name = accountInfo.username.split('@')[0]
    } else {
      name = accountInfo.username
    }
  }

  // 如果还是没有名称，使用ID的一部分
  if (!name) {
    name = accountInfo.id ? accountInfo.id.substring(0, 8) : '未知账号'
  }

  return name
}

const getFullDownloadPath = () => {
  if (selectedBenchmarkAccounts.value.length === 0) {
    return downloadConfig.basePath + '关联账号的平台/关联账号的名称/对标账号名称/发布月份/'
  }
  const benchmarkAccount = selectedBenchmarkAccounts.value[0]
  const currentMonth = new Date().toISOString().slice(0, 7) // YYYY-MM

  // 需要获取关联账号信息来构建正确的路径
  const ourAccountInfo = benchmarkAccount.our_account_info
  if (ourAccountInfo && ourAccountInfo.status !== 'not_found') {
    // 获取关联账号的平台名称
    const ourPlatformName = getPlatformDisplayName(ourAccountInfo.platform_id)
    // 获取关联账号的显示名称
    const ourAccountName = formatOurAccountName(ourAccountInfo)

    return `${downloadConfig.basePath}${ourPlatformName}/${ourAccountName}/${benchmarkAccount.account_name}/${currentMonth}/`
  } else {
    // 如果没有关联账号信息，使用默认结构
    return `${downloadConfig.basePath}未关联平台/未关联账号/${benchmarkAccount.account_name}/${currentMonth}/`
  }
}

// 文件相关工具方法
// 获取文件扩展名（优先使用extension字段，如果为null则从文件名提取）
const getFileExtension = (file: FileItem): string | null => {
  // 优先使用file.extension
  if (file.extension) {
    return file.extension
  }

  // 如果extension为null且不是目录，从文件名提取
  if (!file.is_directory && file.name.includes('.')) {
    return file.name.split('.').pop()?.toLowerCase() || null
  }

  return null
}

const getFileTypeText = (extension: string) => {
  const typeMap = {
    'mp4': '视频',
    'avi': '视频',
    'mov': '视频',
    'wmv': '视频',
    'flv': '视频',
    'webm': '视频',
    'mkv': '视频',
    'jpg': '图片',
    'jpeg': '图片',
    'png': '图片',
    'gif': '图片',
    'bmp': '图片',
    'webp': '图片',
    'svg': '图片',
    'mp3': '音频',
    'wav': '音频',
    'flac': '音频',
    'aac': '音频',
    'ogg': '音频',
    'wma': '音频',
    'txt': '文档',
    'doc': '文档',
    'docx': '文档',
    'pdf': '文档',
    'xls': '文档',
    'xlsx': '文档'
  }
  return typeMap[extension.toLowerCase() as keyof typeof typeMap] || '其他'
}

const getPlatformColor = (platform: string) => {
  const colorMap = {
    'youtube': 'danger',
    'tiktok': '',
    'douyin': 'warning',
    'instagram': 'success',
    'facebook': 'primary',
    'twitter': 'info'
  }
  return colorMap[platform as keyof typeof colorMap] || 'info'
}

const getPlatformText = (platform: string) => {
  const textMap = {
    'youtube': 'YouTube',
    'tiktok': 'TikTok',
    'douyin': '抖音',
    'instagram': 'Instagram',
    'facebook': 'Facebook',
    'twitter': 'Twitter'
  }
  return textMap[platform as keyof typeof textMap] || platform
}

const getCoreServiceStatusColor = (status: string) => {
  const colorMap = {
    'running': 'success',
    'active': 'success',
    'stopped': 'danger',
    'error': 'danger',
    'starting': 'warning',
    'unknown': ''  // 使用默认颜色，表示这是正常状态
  }
  return colorMap[status as keyof typeof colorMap] || ''
}

const getCoreServiceStatusText = (status: string) => {
  const textMap = {
    'running': '运行中',
    'active': '活跃',
    'stopped': '已停止',
    'error': '错误',
    'starting': '启动中',
    'unknown': '可用'  // 改为更友好的文本
  }
  return textMap[status as keyof typeof textMap] || '可用'
}

// 业务方法
const loadFileList = async () => {
  loading.value = true
  try {
    console.log('开始加载文件列表')

    // 1. 首先获取所有Core服务
    await loadCoreServices()

    if (coreServices.value.length === 0) {
      console.log('没有可用的Core服务')
      ElMessage.warning('没有可用的Core服务')
      fileList.value = []
      totalCount.value = 0
      stats.value.total_count = 0
      return
    }

    // 2. 确定要查询的Core服务
    let servicesToQuery = coreServices.value
    if (selectedCoreService.value) {
      // 如果选择了特定的Core服务，只查询该服务
      const selectedService = coreServices.value.find(s => s.id === selectedCoreService.value)
      if (selectedService) {
        servicesToQuery = [selectedService]
        console.log(`只查询选中的Core服务: ${selectedService.name}`)
      } else {
        console.warn(`找不到选中的Core服务: ${selectedCoreService.value}`)
        servicesToQuery = []
      }
    }

    // 3. 从Core服务获取文件列表
    const allFiles: FileItem[] = []

    // 直接使用通用的文件系统API，和发布管理页面的loadPathContents方法一样
    try {
      console.log('正在获取文件列表...')

      const response = await listFolderContents(
        currentFolderPath.value,
        undefined,
        true, // 始终计算MD5，用于MD5记录状态显示
        showAdvancedInfo.value // 媒体信息根据用户选择
      )
      console.log('文件系统API响应:', response)

      // 正确处理响应格式：response.data.files
      const files = response.data?.files || []

      // 为每个文件添加Core服务信息
      files.forEach((file: any) => {
        // 根据文件路径自动判断所属的Core服务
        const coreServiceName = determineCoreServiceFromPath(file.path)

        allFiles.push({
          name: file.name,
          path: file.path,
          size: file.size || 0,
          is_directory: file.is_directory || false,
          extension: file.extension,
          last_modified: file.last_modified ? new Date(parseFloat(file.last_modified) * 1000).toISOString() : new Date().toISOString(),
          core_service: coreServiceName,
          platform: extractPlatformFromPath(file.path),
          content_type: getContentTypeFromExtension(file.extension),
          md5_hash: file.md5_hash,
          is_uploaded: file.is_uploaded,
          media_info: file.media_info
        })
      })

      console.log(`获取到 ${files.length} 个文件`)
    } catch (error) {
      console.error('获取文件列表失败:', error)
    }



    // 3. 更新文件列表
    fileList.value = allFiles
    totalCount.value = allFiles.length
    stats.value.total_count = allFiles.length

    // 4. 等待MD5记录缓存加载完成，确保状态显示正确
    try {
      console.log('🚀 准备调用 loadFolderMD5Records...')
      console.log('🚀 当前文件列表长度:', fileList.value.length)
      console.log('🚀 当前文件夹路径:', currentFolderPath.value)
      await loadFolderMD5Records()
      console.log('✅ loadFolderMD5Records 调用完成')
    } catch (error) {
      console.error('❌ loadFolderMD5Records 调用失败:', error)
      console.error('❌ 错误详情:', error.message)
      console.error('❌ 错误堆栈:', error.stack)
    }

    // 5. 强制更新界面，确保MD5状态正确显示
    await nextTick()

    if (allFiles.length === 0) {
      ElMessage.info('H:\\PublishSystem目录为空或不存在')
    } else {
      if (selectedCoreService.value) {
        const selectedService = coreServices.value.find(s => s.id === selectedCoreService.value)
        console.log(`查看Core服务 ${selectedService?.name} 的文件，共 ${allFiles.length} 个文件/文件夹`)
      } else {
        console.log(`总共加载了 ${allFiles.length} 个文件/文件夹`)
      }
    }

  } catch (error) {
    console.error('加载文件列表失败:', error)
    ElMessage.error(`加载文件列表失败: ${error.message}`)

    // 清空列表
    fileList.value = []
    totalCount.value = 0
    stats.value.total_count = 0
  } finally {
    loading.value = false

    // 应用Intersection Observer到新加载的视频文件
    await nextTick()
    applyIntersectionObserver()
  }
}

// 加载Core服务列表
const loadCoreServices = async () => {
  try {
    console.log('正在获取Core服务列表...')

    const res = await getCoreServices()
    console.log('Core服务API响应:', res)

    // 正确处理响应数据，参考其他页面的实现
    const coreServicesData = res.data || res

    // 确保每个Core服务都有id字段
    const formattedCoreServices = Array.isArray(coreServicesData) ? coreServicesData.map(service => {
      // 如果服务没有id字段，但有_id字段，则使用_id作为id
      if (!service.id && service._id) {
        return {
          ...service,
          id: service._id
          // 不设置默认status，让前端显示为"可用"
        }
      }
      return {
        ...service
        // 不设置默认status，让前端显示为"可用"
      }
    }) : []

    coreServices.value = formattedCoreServices

    console.log(`找到 ${coreServices.value.length} 个Core服务:`, coreServices.value)

    if (coreServices.value.length === 0) {
      console.warn('没有找到Core服务，请确保Core服务已正确注册到Consul')
      ElMessage.warning('未找到可用的Core服务，请确保Core服务已正确注册到Consul')
    } else {
      // 检查Core服务数据结构
      console.log('Core服务数据示例:', coreServices.value[0])
    }
  } catch (error) {
    console.error('获取Core服务列表失败:', error)
    ElMessage.error('获取Core服务列表失败，请检查网络连接和后端服务状态')
    coreServices.value = []
  }
}

// 根据文件路径判断所属的Core服务
const determineCoreServiceFromPath = (filePath: string): string => {
  console.log(`正在为文件路径匹配Core服务: ${filePath}`)
  console.log(`当前可用的Core服务:`, coreServices.value.map(s => s.name))

  // 从路径中提取设备名称，通常在路径的第三或第四部分
  // 例如: H:\PublishSystem\youtube\B-HK-1-2-23-002\...
  const pathParts = filePath.split(/[\\\/]/).filter(part => part.length > 0)
  console.log(`路径分割结果:`, pathParts)

  if (pathParts.length >= 3) {
    // 通常设备名称在第三个位置（索引2）
    const deviceName = pathParts[2]
    console.log(`提取的设备名称: ${deviceName}`)

    // 在Core服务列表中查找匹配的服务
    const matchingService = coreServices.value.find(service => {
      const isExactMatch = service.name === deviceName
      const serviceIncludesDevice = service.name.includes(deviceName)
      const deviceIncludesService = deviceName.includes(service.name)

      console.log(`检查服务 ${service.name}:`, {
        isExactMatch,
        serviceIncludesDevice,
        deviceIncludesService
      })

      return isExactMatch || serviceIncludesDevice || deviceIncludesService
    })

    if (matchingService) {
      console.log(`✅ 文件 ${filePath} 匹配到Core服务: ${matchingService.name}`)
      return matchingService.name
    } else {
      console.log(`❌ 设备名称 ${deviceName} 没有匹配到任何Core服务`)
    }
  }

  // 如果没有找到匹配的Core服务，返回第一个可用的Core服务
  if (coreServices.value.length > 0) {
    console.log(`⚠️ 文件 ${filePath} 使用默认Core服务: ${coreServices.value[0].name}`)
    return coreServices.value[0].name
  }

  // 如果没有任何Core服务，返回通用文件系统
  console.warn(`❌ 文件 ${filePath} 无法匹配Core服务，使用通用文件系统`)
  return '通用文件系统'
}

// 从文件路径提取平台信息
const extractPlatformFromPath = (path: string): string => {
  const pathParts = path.split(/[\\\/]/)
  // 查找可能的平台名称
  for (const part of pathParts) {
    const lowerPart = part.toLowerCase()
    if (['youtube', 'tiktok', 'douyin', 'instagram', 'facebook'].includes(lowerPart)) {
      return lowerPart
    }
  }
  return 'unknown'
}

// 从文件路径提取账号信息
const extractAccountFromPath = (path: string): string => {
  const pathParts = path.split(/[\\\/]/)
  // 路径格式通常是: H:\PublishSystem\youtube\B-HK-1-2-23-002
  // 我们需要找到平台后面的账号名称
  for (let i = 0; i < pathParts.length - 1; i++) {
    const part = pathParts[i].toLowerCase()
    if (['youtube', 'tiktok', 'douyin', 'instagram', 'facebook'].includes(part)) {
      // 返回平台后面的部分作为账号名称
      if (i + 1 < pathParts.length) {
        const accountName = pathParts[i + 1]
        // 如果账号名称不为空且不是常见的文件夹名称，则返回账号名称
        if (accountName && accountName.trim() &&
            !['content', 'videos', 'images', 'temp', 'backup'].includes(accountName.toLowerCase())) {
          return accountName
        }
      }
    }
  }

  // 如果没有找到平台特定的账号，尝试从路径的最后几个部分提取可能的账号名称
  // 例如：H:\PublishSystem\B-HK-1-2-23-002\content -> B-HK-1-2-23-002
  for (let i = pathParts.length - 1; i >= 0; i--) {
    const part = pathParts[i]
    // 检查是否像账号名称（包含字母、数字、连字符等）
    if (part && /^[A-Za-z0-9\-_]+$/.test(part) &&
        !['content', 'videos', 'images', 'temp', 'backup', 'publishsystem'].includes(part.toLowerCase()) &&
        part.length > 3) {
      return part
    }
  }

  return '未识别账号'
}

// 获取当前路径的平台和账号信息（增强版）
const getCurrentPathInfo = async () => {
  const platform = extractPlatformFromPath(currentFolderPath.value)
  const extractedAccount = extractAccountFromPath(currentFolderPath.value)

  // 尝试获取真实的账号信息
  // 这里我们可以根据路径中的账号名称来查找对应的真实账号
  try {
    if (extractedAccount !== '未识别账号' && platform !== 'unknown') {
      // 搜索匹配的账号
      const accountsResponse = await getAccounts({
        platform_id: platform // 这里需要平台ID，可能需要进一步映射
      })

      if (accountsResponse && accountsResponse.data) {
        // 查找名称匹配的账号
        const matchedAccount = accountsResponse.data.find(acc =>
          acc.display_name === extractedAccount ||
          acc.username === extractedAccount ||
          acc.username?.includes(extractedAccount)
        )

        if (matchedAccount) {
          return {
            platform,
            account: matchedAccount.display_name || matchedAccount.username || extractedAccount,
            realAccount: matchedAccount
          }
        }
      }
    }
  } catch (error) {
    console.error('获取真实账号信息失败:', error)
  }

  return { platform, account: extractedAccount, realAccount: null }
}

// 同步版本的getCurrentPathInfo（用于不需要异步的场景）
const getCurrentPathInfoSync = () => {
  const platform = extractPlatformFromPath(currentFolderPath.value)
  const account = extractAccountFromPath(currentFolderPath.value)
  return { platform, account }
}

// 获取真实的关联账号信息
const getRealAccountInfo = async (deviceId?: string, platformName?: string) => {
  try {
    // 如果有设备ID，尝试获取设备关联的账号
    if (deviceId) {
      const cacheKey = `device_${deviceId}_${platformName || 'all'}`

      // 检查缓存
      if (accountInfoCache.value.has(cacheKey)) {
        return accountInfoCache.value.get(cacheKey)
      }

      // 调用API获取设备关联的账号
      const response = await getDeviceAccounts(deviceId, platformName)
      if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {
        const accountMapping = response.data[0]

        // 获取账号详细信息
        const accountsResponse = await getAccounts({
          platform_id: accountMapping.platform_id
        })

        if (accountsResponse && accountsResponse.data && accountsResponse.data.length > 0) {
          const account = accountsResponse.data.find(acc => acc.id === accountMapping.account_id)
          if (account) {
            const accountInfo = {
              id: account.id,
              name: account.display_name || account.username || account.id,
              platform: platformName || 'unknown',
              platform_id: accountMapping.platform_id
            }

            // 缓存结果
            accountInfoCache.value.set(cacheKey, accountInfo)
            return accountInfo
          }
        }
      }
    }

    return null
  } catch (error) {
    console.error('获取真实账号信息失败:', error)
    return null
  }
}

// 根据文件扩展名获取内容类型
const getContentTypeFromExtension = (extension?: string): string => {
  if (!extension) return 'unknown'

  const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv']
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']
  const audioExts = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma']

  const ext = extension.toLowerCase()
  if (videoExts.includes(ext)) return 'video'
  if (imageExts.includes(ext)) return 'image'
  if (audioExts.includes(ext)) return 'audio'

  return 'document'
}



const loadStats = async () => {
  try {
    // 调用统计API
    const response = await fetch('/api/v1/content/stats/summary', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    if (response.ok) {
      const data = await response.json()
      stats.value = {
        total_count: data.total_count || 0,
        recent_count: data.recent_count || 0,
        platform_stats: data.platform_stats || []
      }
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    // 统计数据加载失败不显示错误，使用默认值
  }
}

const refreshData = async () => {
  await Promise.all([loadFileList(), loadStats()])
  ElMessage.success('数据刷新成功')
}



// 测试Core服务连接
const testCoreServices = async () => {
  if (coreServices.value.length === 0) {
    ElMessage.warning('没有可用的Core服务')
    return
  }

  ElMessage.info('正在测试Core服务连接...')

  for (const service of coreServices.value) {
    try {
      console.log(`测试Core服务: ${service.name}`)

      // 测试Core服务的健康检查API
      const response = await fetch(`/api/v1/cores/${service.id}/health`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (response.ok) {
        console.log(`✅ Core服务 ${service.name} 连接正常`)
        ElMessage.success(`Core服务 ${service.name} 连接正常`)
      } else {
        console.log(`❌ Core服务 ${service.name} 连接失败: ${response.status}`)
        ElMessage.error(`Core服务 ${service.name} 连接失败`)
      }
    } catch (error) {
      console.error(`❌ Core服务 ${service.name} 测试失败:`, error)
      ElMessage.error(`Core服务 ${service.name} 测试失败`)
    }
  }
}

const handleSearch = () => {
  // 搜索防抖
  setTimeout(() => {
    loadFileList()
  }, 500)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  // 不需要重新加载数据，分页在前端处理
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  // 不需要重新加载数据，分页在前端处理
}

const handleSelectionChange = (selection: FileItem[]) => {
  selectedItems.value = selection
}

const viewContentDetail = (file: FileItem) => {
  selectedContent.value = {
    title: file.name,
    platform: file.platform || '未知平台',
    content_type: file.content_type || '未知类型',
    author: { name: file.core_service || '未知服务' },
    file_info: {
      file_size: file.size,
      file_format: file.extension || '未知格式'
    },
    description: `文件路径: ${file.path}`
  }
  showDetailDialog.value = true
}

const editFile = (file: FileItem) => {
  // TODO: 实现文件编辑功能
  ElMessage.info('文件编辑功能开发中...')
}

// 文件操作相关方法（已移动到文件管理增强功能部分）

const viewFileDetail = (file: FileItem) => {
  console.log('查看文件详情:', file)
  selectedContent.value = {
    title: file.name,
    platform: file.platform || '未知平台',
    content_type: file.content_type || '未知类型',
    author: { name: file.core_service || '未知服务' },
    file_info: {
      file_size: file.size,
      file_format: file.extension || '未知格式'
    },
    description: `文件路径: ${file.path}`
  }
  showDetailDialog.value = true
}



const deleteFile = async (file: FileItem) => {
  try {
    const confirmMessage = file.md5_hash
      ? `确定要删除 ${file.is_directory ? '目录' : '文件'} "${file.name}" 吗？\n\n注意：文件将被物理删除，但MD5记录会保留。`
      : `确定要删除 ${file.is_directory ? '目录' : '文件'} "${file.name}" 吗？`

    await ElMessageBox.confirm(
      confirmMessage,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消'
      }
    )

    console.log('删除文件:', file.path)

    // 使用新的删除文件API（通过Core服务删除）
    const response = await deleteFileAPI(file.path)

    if (response.data && response.data.success) {
      ElMessage.success(`删除成功，共删除 ${response.data.deleted_count} 个项目`)

      if (response.data.errors && response.data.errors.length > 0) {
        console.warn('删除过程中的错误:', response.data.errors)
        ElMessage.warning(`部分文件删除失败，请查看控制台`)
      }

      // 刷新文件列表
      loadFileList()
    } else {
      throw new Error('删除失败：服务器返回异常')
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文件失败:', error)
      ElMessage.error(`删除失败: ${(error as any)?.message || error}`)
    }
    // 用户取消删除时不显示错误
  }
}

const createDownloadTask = async () => {
  if (!taskForm.name || !taskForm.platform || !taskForm.urls) {
    ElMessage.warning('请填写完整信息')
    return
  }

  try {
    // 调用创建任务API
    const response = await fetch('/api/v1/download/tasks', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        task_name: taskForm.name,
        task_type: 'batch',
        source_urls: taskForm.urls.split('\n').filter(url => url.trim()),
        target_platform: taskForm.platform,
        download_config: {
          quality: 'best',
          format: 'mp4',
          include_subtitles: false,
          include_thumbnail: true,
          include_metadata: true
        }
      })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    ElMessage.success('下载任务创建成功')
    showCreateTaskDialog.value = false

    // 重置表单
    taskForm.name = ''
    taskForm.platform = ''
    taskForm.urls = ''

    // 刷新文件列表
    loadFileList()

  } catch (error) {
    console.error('创建下载任务失败:', error)
    ElMessage.error(`创建下载任务失败: ${error.message}`)
  }
}

const createCategory = () => {
  if (!categoryForm.name) {
    ElMessage.warning('请输入分类名称')
    return
  }

  // TODO: 调用创建分类API
  ElMessage.success('分类创建成功（模拟）')
  showCategoryDialog.value = false

  // 重置表单
  categoryForm.name = ''
  categoryForm.description = ''
}

// 文件管理增强功能方法
const openDirectory = (folder: FileItem) => {
  if (!folder.is_directory) {
    ElMessage.warning('只能打开文件夹')
    return
  }

  currentFolderPath.value = folder.path
  loadFileList()
}

const goToParentDirectory = () => {
  const parentPath = currentFolderPath.value.split('\\').slice(0, -1).join('\\')
  if (parentPath && parentPath !== currentFolderPath.value) {
    currentFolderPath.value = parentPath
    loadFileList()
  }
}

const toggleAdvancedInfo = () => {
  showAdvancedInfo.value = !showAdvancedInfo.value
  loadFileList()  // 重新加载以获取MD5和媒体信息
}

// 预览视频
const previewVideo = (file: FileItem) => {
  if (!file || file.is_directory) {
    ElMessage.warning('请选择一个视频文件')
    return
  }

  if (!isVideoFile(file)) {
    ElMessage.warning('该文件不是支持的视频格式')
    return
  }

  selectedVideoForPreview.value = file
  showVideoPreviewDialog.value = true
}

// 视频缩略图缓存
const thumbnailCache = ref<Map<string, string>>(new Map())
const thumbnailGenerating = ref<Set<string>>(new Set())
const visibleVideoFiles = ref<Set<string>>(new Set())

// Intersection Observer for lazy loading
let intersectionObserver: IntersectionObserver | null = null

// 缩略图尺寸缓存
const thumbnailSizeCache = ref(new Map<string, { width: number; height: number }>())

// 获取视频缩略图URL
const getVideoThumbnailUrl = (file: FileItem): string => {
  if (!file || file.is_directory || !isVideoFile(file)) {
    return ''
  }

  const cacheKey = `${file.path}_${file.size}_${file.last_modified}`

  // 检查缓存
  if (thumbnailCache.value.has(cacheKey)) {
    return thumbnailCache.value.get(cacheKey) || ''
  }

  // 如果正在生成中，返回空
  if (thumbnailGenerating.value.has(cacheKey)) {
    return ''
  }

  // 只有当文件在可见区域时才生成缩略图（懒加载）
  if (visibleVideoFiles.value.has(file.path)) {
    generateThumbnailAsync(file, cacheKey)
  }

  return ''
}

// 获取缩略图样式
const getThumbnailStyle = (file: FileItem): Record<string, string> => {
  const cacheKey = `${file.path}_${file.size}_${file.last_modified}`
  const sizeInfo = thumbnailSizeCache.value.get(cacheKey)

  if (!sizeInfo || sizeInfo.width === 0 || sizeInfo.height === 0) {
    return {}
  }

  // 计算合适的显示尺寸，保持比例
  const maxWidth = 60
  const maxHeight = 60
  const aspectRatio = sizeInfo.width / sizeInfo.height

  let displayWidth = maxWidth
  let displayHeight = maxHeight

  if (aspectRatio > 1) {
    // 横版：以宽度为准
    displayHeight = displayWidth / aspectRatio
    if (displayHeight > maxHeight) {
      displayHeight = maxHeight
      displayWidth = displayHeight * aspectRatio
    }
  } else {
    // 竖版或正方形：以高度为准
    displayWidth = displayHeight * aspectRatio
    if (displayWidth > maxWidth) {
      displayWidth = maxWidth
      displayHeight = displayWidth / aspectRatio
    }
  }

  return {
    width: `${Math.round(displayWidth)}px`,
    height: `${Math.round(displayHeight)}px`
  }
}

// 异步生成缩略图
const generateThumbnailAsync = async (file: FileItem, cacheKey: string) => {
  if (thumbnailGenerating.value.has(cacheKey)) {
    return
  }

  thumbnailGenerating.value.add(cacheKey)

  try {
    const { generateVideoThumbnail } = await import('@/api/social')

    const response = await generateVideoThumbnail({
      video_path: file.path,
      max_width: 120,
      max_height: 68,
      quality: 75
    })

    if (response.data.success && response.data.thumbnail_url) {
      // 使用HTTP URL
      const thumbnailUrl = response.data.thumbnail_url
      thumbnailCache.value.set(cacheKey, thumbnailUrl)

      // 保存缩略图尺寸信息
      if (response.data.actual_width && response.data.actual_height) {
        thumbnailSizeCache.value.set(cacheKey, {
          width: response.data.actual_width,
          height: response.data.actual_height
        })
      }

      // 触发界面更新
      await nextTick()
    }
  } catch (error) {
    console.error('生成缩略图失败:', error)
  } finally {
    thumbnailGenerating.value.delete(cacheKey)
  }
}

// 处理缩略图加载错误
const handleThumbnailError = (event: Event) => {
  console.warn('缩略图加载失败:', event)
  // 可以在这里设置默认图片或隐藏缩略图
}

// 初始化Intersection Observer
const initIntersectionObserver = () => {
  if (intersectionObserver) {
    intersectionObserver.disconnect()
  }

  intersectionObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        const filePath = entry.target.getAttribute('data-file-path')
        if (filePath) {
          if (entry.isIntersecting) {
            visibleVideoFiles.value.add(filePath)
            // 触发缩略图生成
            const file = fileList.value.find(f => f.path === filePath)
            if (file && isVideoFile(file)) {
              getVideoThumbnailUrl(file)
            }
          } else {
            visibleVideoFiles.value.delete(filePath)
          }
        }
      })
    },
    {
      root: null,
      rootMargin: '50px',
      threshold: 0.1
    }
  )
}

// 应用Intersection Observer到视频缩略图容器
const applyIntersectionObserver = () => {
  if (!intersectionObserver) return

  // 观察所有视频缩略图容器
  const videoContainers = document.querySelectorAll('.video-thumbnail-container[data-file-path]')
  videoContainers.forEach(container => {
    intersectionObserver!.observe(container)
  })
}

// 清理缓存
const clearThumbnailCache = () => {
  thumbnailCache.value.clear()
  thumbnailSizeCache.value.clear()
  thumbnailGenerating.value.clear()
  visibleVideoFiles.value.clear()

  if (intersectionObserver) {
    intersectionObserver.disconnect()
  }
}

// 处理发布处理命令
const handlePublishCommand = (command: string) => {
  switch (command) {
    case 'info':
      showAdvancedInfo.value = !showAdvancedInfo.value
      loadFileList() // 重新加载文件列表以显示/隐藏详细信息
      break
    case 'rename':
      openBatchRenameDialog()
      break
    case 'md5':
      showMD5ManagementDialog.value = true
      break
    case 'publish':
      openBatchPublishStatusDialog()
      break
    case 'archive':
      openArchivePublishedDialog()
      break
    case 'category':
      showCategoryDialog.value = true
      break
    default:
      console.warn('未知的发布处理命令:', command)
  }
}

// 处理视频处理命令
const handleVideoProcessCommand = (command: string) => {
  switch (command) {
    case 'merge':
      openVideoMergeDialog()
      break
    case 'triple':
      openTripleVideoDialog()
      break
    case 'watermark':
      openWatermarkProcessDialog()
      break
    case 'rotate':
      openVideoRotationDialog()
      break
    case 'accelerate':
      openVideoAccelerationDialog()
      break
    default:
      console.warn('未知的视频处理命令:', command)
  }
}

// 处理音频处理命令
const handleAudioProcessCommand = (command: string) => {
  switch (command) {
    case 'subtitles':
      openGenerateSubtitlesDialog()
      break
    case 'extract':
      openExtractAudioDialog()
      break
    case 'vocals':
      openSeparateVocalsDialog()
      break
    case 'replace':
      openReplaceAudioDialog()
      break
    default:
      console.warn('未知的音频处理命令:', command)
  }
}

// 打开视频旋转对话框
const openVideoRotationDialog = () => {
  if (selectedVideoFiles.value.length === 0) {
    ElMessage.warning('请先选择要旋转的视频文件')
    return
  }

  // 重置表单
  videoRotationForm.rotationAngle = 90
  videoRotationForm.outputQuality = 'medium'
  videoRotationForm.overwriteOriginal = false
  videoRotationForm.outputSuffix = '_rotated'

  showVideoRotationDialog.value = true
}

// 执行视频旋转
const executeVideoRotation = async () => {
  if (selectedVideoFiles.value.length === 0) {
    ElMessage.warning('没有选中的视频文件')
    return
  }

  try {
    videoRotationLoading.value = true

    // 构建请求数据
    const requestData = {
      video_paths: selectedVideoFiles.value.map(file => file.path),
      rotation_angle: videoRotationForm.rotationAngle,
      output_quality: videoRotationForm.outputQuality,
      overwrite_original: videoRotationForm.overwriteOriginal,
      output_suffix: videoRotationForm.outputSuffix
    }

    console.log('发送视频旋转请求:', requestData)

    const response = await fetch('/api/v1/filesystem/rotate-videos', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(requestData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.detail || '视频旋转失败')
    }

    const result = await response.json()
    console.log('视频旋转响应:', result)

    if (result.success) {
      ElMessage.success(`视频旋转完成！成功 ${result.successful_count} 个，失败 ${result.failed_count} 个`)

      // 显示详细结果
      if (result.failed_count > 0) {
        const failedFiles = result.results.filter((r: any) => !r.success)
        const failedMessages = failedFiles.map((r: any) => `${r.original_path}: ${r.error_message}`).join('\n')
        ElNotification({
          title: '部分文件旋转失败',
          message: failedMessages,
          type: 'warning',
          duration: 10000
        })
      }

      // 关闭对话框并刷新文件列表
      showVideoRotationDialog.value = false
      await loadFileList()
    } else {
      throw new Error(result.error || '视频旋转失败')
    }

  } catch (error: any) {
    console.error('视频旋转失败:', error)
    ElMessage.error(`视频旋转失败: ${error.message}`)
  } finally {
    videoRotationLoading.value = false
  }
}

// 打开视频加速对话框
const openVideoAccelerationDialog = () => {
  if (selectedVideoFiles.value.length === 0) {
    ElMessage.warning('请先选择要加速的视频文件')
    return
  }

  // 重置表单
  videoAccelerationForm.targetDuration = 59
  videoAccelerationForm.outputQuality = 'medium'
  videoAccelerationForm.overwriteOriginal = false
  videoAccelerationForm.outputSuffix = '_accelerated'

  showVideoAccelerationDialog.value = true
}

// 执行视频加速
const executeVideoAcceleration = async () => {
  if (selectedVideoFiles.value.length === 0) {
    ElMessage.warning('没有选中的视频文件')
    return
  }

  videoAccelerationLoading.value = true

  try {
    const requestData = {
      video_paths: selectedVideoFiles.value.map(file => file.path),
      target_duration: videoAccelerationForm.targetDuration,
      output_quality: videoAccelerationForm.outputQuality,
      overwrite_original: videoAccelerationForm.overwriteOriginal,
      output_suffix: videoAccelerationForm.outputSuffix
    }

    console.log('发送视频加速请求:', requestData)

    const response = await fetch('/api/v1/filesystem/accelerate-videos', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.detail || '视频加速失败')
    }

    const result = await response.json()
    console.log('视频加速响应:', result)

    if (result.success) {
      ElMessage.success(`视频加速完成！成功 ${result.successful_count} 个，失败 ${result.failed_count} 个`)

      // 显示详细结果
      if (result.failed_count > 0) {
        const failedFiles = result.results.filter((r: any) => !r.success)
        const failedMessages = failedFiles.map((r: any) => `${r.original_path}: ${r.error_message}`).join('\n')
        ElMessageBox.alert(failedMessages, '部分文件加速失败', {
          confirmButtonText: '确定',
          type: 'warning',
          customStyle: {
            maxHeight: '400px',
            overflow: 'auto'
          }
        })
      }

      // 显示加速统计信息
      const successResults = result.results.filter((r: any) => r.success)
      if (successResults.length > 0) {
        const avgSpeedFactor = successResults.reduce((sum: number, r: any) => sum + r.speed_factor, 0) / successResults.length
        ElMessage.info(`平均加速倍率: ${avgSpeedFactor.toFixed(2)}x`, {
          duration: 5000
        })
      }

      // 关闭对话框并刷新文件列表
      showVideoAccelerationDialog.value = false
      await loadFileList()
    } else {
      throw new Error(result.error || '视频加速失败')
    }

  } catch (error: any) {
    console.error('视频加速失败:', error)
    ElMessage.error(`视频加速失败: ${error.message}`)
  } finally {
    videoAccelerationLoading.value = false
  }
}

// ==================== 音频处理相关函数 ====================

// 打开生成字幕对话框
const openGenerateSubtitlesDialog = () => {
  // 重置表单
  generateSubtitlesForm.outputFormat = 'srt'
  generateSubtitlesForm.language = 'auto'
  generateSubtitlesForm.modelSize = 'base'
  generateSubtitlesForm.maxConcurrent = 2

  showGenerateSubtitlesDialog.value = true
}

// 执行批量生成字幕
const executeGenerateSubtitles = async () => {
  if (!currentFolderPath.value) {
    ElMessage.warning('请先选择文件夹')
    return
  }

  generateSubtitlesLoading.value = true
  try {
    const requestData: GenerateSubtitlesRequest = {
      folder_path: currentFolderPath.value,
      output_format: generateSubtitlesForm.outputFormat,
      language: generateSubtitlesForm.language,
      model_size: generateSubtitlesForm.modelSize,
      max_concurrent: generateSubtitlesForm.maxConcurrent
    }

    console.log('发送生成字幕请求:', requestData)

    const response = await generateSubtitlesBatch(requestData)
    const result = response.data
    console.log('生成字幕响应:', result)

    if (result.success) {
      ElMessage.success(`字幕生成完成！处理了 ${result.processed_files} 个文件，成功 ${result.successful_count} 个`)

      // 显示详细结果
      if (result.failed_count > 0) {
        const failedFiles = result.results.filter(r => !r.success)
        const failedMessages = failedFiles.map(r => `${r.input_file}: ${r.error_message}`).join('\n')
        ElNotification({
          title: '部分文件处理失败',
          message: failedMessages,
          type: 'warning',
          duration: 10000
        })
      }

      // 关闭对话框并刷新文件列表
      showGenerateSubtitlesDialog.value = false
      await loadFileList()
    } else {
      throw new Error(result.error || '字幕生成失败')
    }

  } catch (error: any) {
    console.error('生成字幕失败:', error)
    ElMessage.error(`生成字幕失败: ${error.message}`)
  } finally {
    generateSubtitlesLoading.value = false
  }
}

// 打开分离音频对话框
const openExtractAudioDialog = () => {
  // 重置表单
  extractAudioForm.outputFormat = 'wav'
  extractAudioForm.quality = 'high'
  extractAudioForm.maxConcurrent = 3

  showExtractAudioDialog.value = true
}

// 执行批量分离音频
const executeExtractAudio = async () => {
  if (!currentFolderPath.value) {
    ElMessage.warning('请先选择文件夹')
    return
  }

  extractAudioLoading.value = true
  try {
    const requestData: ExtractAudioRequest = {
      folder_path: currentFolderPath.value,
      output_format: extractAudioForm.outputFormat,
      quality: extractAudioForm.quality,
      max_concurrent: extractAudioForm.maxConcurrent
    }

    console.log('发送分离音频请求:', requestData)

    const response = await extractAudioBatch(requestData)
    const result = response.data
    console.log('分离音频响应:', result)

    if (result.success) {
      ElMessage.success(`音频分离完成！处理了 ${result.processed_files} 个文件，成功 ${result.successful_count} 个`)

      // 显示详细结果
      if (result.failed_count > 0) {
        const failedFiles = result.results.filter(r => !r.success)
        const failedMessages = failedFiles.map(r => `${r.input_file}: ${r.error_message}`).join('\n')
        ElNotification({
          title: '部分文件处理失败',
          message: failedMessages,
          type: 'warning',
          duration: 10000
        })
      }

      // 关闭对话框并刷新文件列表
      showExtractAudioDialog.value = false
      await loadFileList()
    } else {
      throw new Error(result.error || '音频分离失败')
    }

  } catch (error: any) {
    console.error('分离音频失败:', error)
    ElMessage.error(`分离音频失败: ${error.message}`)
  } finally {
    extractAudioLoading.value = false
  }
}

// 打开人声分离对话框
const openSeparateVocalsDialog = () => {
  // 重置表单
  separateVocalsForm.separationMethod = 'ffmpeg'
  separateVocalsForm.outputQuality = 'high'
  separateVocalsForm.maxConcurrent = 2

  showSeparateVocalsDialog.value = true
}

// 执行批量人声分离
const executeSeparateVocals = async () => {
  if (!currentFolderPath.value) {
    ElMessage.warning('请先选择文件夹')
    return
  }

  separateVocalsLoading.value = true
  try {
    const requestData: SeparateVocalsRequest = {
      folder_path: currentFolderPath.value,
      separation_method: separateVocalsForm.separationMethod,
      output_quality: separateVocalsForm.outputQuality,
      max_concurrent: separateVocalsForm.maxConcurrent
    }

    console.log('发送人声分离请求:', requestData)

    const response = await separateVocalsBatch(requestData)
    const result = response.data
    console.log('人声分离响应:', result)

    if (result.success) {
      ElMessage.success(`人声分离完成！处理了 ${result.processed_files} 个文件，成功 ${result.successful_count} 个`)

      // 显示详细结果
      if (result.failed_count > 0) {
        const failedFiles = result.results.filter(r => !r.success)
        const failedMessages = failedFiles.map(r => `${r.input_file}: ${r.error_message}`).join('\n')
        ElNotification({
          title: '部分文件处理失败',
          message: failedMessages,
          type: 'warning',
          duration: 10000
        })
      }

      // 关闭对话框并刷新文件列表
      showSeparateVocalsDialog.value = false
      await loadFileList()
    } else {
      throw new Error(result.error || '人声分离失败')
    }

  } catch (error: any) {
    console.error('人声分离失败:', error)
    ElMessage.error(`人声分离失败: ${error.message}`)
  } finally {
    separateVocalsLoading.value = false
  }
}

// 打开替换音频对话框
const openReplaceAudioDialog = () => {
  // 重置表单
  replaceAudioForm.newAudioPath = ''
  replaceAudioForm.audioVolume = 1.0
  replaceAudioForm.fadeDuration = 0.5
  replaceAudioForm.maxConcurrent = 3

  showReplaceAudioDialog.value = true
}

// 执行批量替换音频
const executeReplaceAudio = async () => {
  if (!currentFolderPath.value) {
    ElMessage.warning('请先选择文件夹')
    return
  }

  if (!replaceAudioForm.newAudioPath) {
    ElMessage.warning('请选择新的音频文件')
    return
  }

  replaceAudioLoading.value = true
  try {
    const requestData: ReplaceAudioRequest = {
      folder_path: currentFolderPath.value,
      new_audio_path: replaceAudioForm.newAudioPath,
      audio_volume: replaceAudioForm.audioVolume,
      fade_duration: replaceAudioForm.fadeDuration,
      max_concurrent: replaceAudioForm.maxConcurrent
    }

    console.log('发送替换音频请求:', requestData)

    const response = await replaceAudioBatch(requestData)
    const result = response.data
    console.log('替换音频响应:', result)

    if (result.success) {
      ElMessage.success(`音频替换完成！处理了 ${result.processed_files} 个文件，成功 ${result.successful_count} 个`)

      // 显示详细结果
      if (result.failed_count > 0) {
        const failedFiles = result.results.filter(r => !r.success)
        const failedMessages = failedFiles.map(r => `${r.input_file}: ${r.error_message}`).join('\n')
        ElNotification({
          title: '部分文件处理失败',
          message: failedMessages,
          type: 'warning',
          duration: 10000
        })
      }

      // 关闭对话框并刷新文件列表
      showReplaceAudioDialog.value = false
      await loadFileList()
    } else {
      throw new Error(result.error || '音频替换失败')
    }

  } catch (error: any) {
    console.error('替换音频失败:', error)
    ElMessage.error(`替换音频失败: ${error.message}`)
  } finally {
    replaceAudioLoading.value = false
  }
}

// 复制当前文件名（在批量重命名对话框中使用）
const copyCurrentFilenames = async () => {
  try {
    let filenames: string[] = []

    if (batchRenameForm.mode === 'selected') {
      // 复制选中文件的文件名（不含扩展名）
      filenames = selectedItems.value
        .filter(f => !f.is_directory)
        .map(f => {
          // 获取不含扩展名的文件名
          const nameWithoutExt = f.name.includes('.')
            ? f.name.substring(0, f.name.lastIndexOf('.'))
            : f.name
          return nameWithoutExt
        })
        .sort() // 确保顺序一致
    } else {
      // 复制当前文件夹所有文件的文件名（不含扩展名）
      const response = await getFilenamesForCopy(currentFolderPath.value)
      filenames = response.data
    }

    if (filenames.length === 0) {
      ElMessage.warning('没有可复制的文件名')
      return
    }

    const filenamesText = filenames.join('\n')

    // 使用统一的复制方法（不显示默认消息）
    await copyToClipboard(filenamesText, false)

    const modeText = batchRenameForm.mode === 'selected' ? '选中' : '当前文件夹所有'
    ElMessage.success(`已复制 ${filenames.length} 个${modeText}文件名到剪贴板`)
  } catch (error: any) {
    console.error('复制文件名失败:', error)
    ElMessage.error(`复制失败: ${error.message || error}`)
  }
}

const openBatchRenameDialog = () => {
  showBatchRenameDialog.value = true
  batchRenameForm.fileNames = ''
  // 默认选择模式：如果有选中文件则选择"选中文件"，否则选择"全部文件"
  batchRenameForm.mode = selectedItems.value.filter(f => !f.is_directory).length > 0 ? 'selected' : 'all'
}

// 获取要重命名的文件数量
const getTargetFileCount = () => {
  if (batchRenameForm.mode === 'selected') {
    return selectedItems.value.filter(f => !f.is_directory).length
  } else {
    return fileList.value.filter(f => !f.is_directory).length
  }
}

// 获取重命名占位符文本
const getRenamePlaceholder = () => {
  const count = getTargetFileCount()
  if (count === 0) {
    return '没有可重命名的文件'
  }

  const examples = []
  for (let i = 1; i <= Math.min(3, count); i++) {
    examples.push(`新文件名${i}`)
  }

  return `请输入新的文件名，每行一个\n例如：\n${examples.join('\n')}`
}

// 重命名模式变化处理
const onRenameModeChange = () => {
  batchRenameForm.fileNames = ''
}

// 打开批量设置发布状态对话框
const openBatchPublishStatusDialog = () => {
  const selectedFiles = selectedItems.value.filter(f => !f.is_directory && f.md5_hash)

  if (selectedFiles.length === 0) {
    ElMessage.warning('请选择有MD5记录的文件')
    return
  }

  // 自动填入当前路径检测到的平台和账号信息
  const pathInfo = getCurrentPathInfoSync()
  batchPublishStatusForm.platform = pathInfo.platform !== 'unknown' ? pathInfo.platform : ''
  batchPublishStatusForm.account = pathInfo.account !== '未识别账号' ? pathInfo.account : ''
  batchPublishStatusForm.is_published = false
  batchPublishStatusForm.publish_date = ''
  batchPublishStatusForm.video_id = ''
  batchPublishStatusForm.video_url = ''
  batchPublishStatusForm.notes = ''

  showBatchPublishStatusDialog.value = true
}

// 执行批量设置发布状态
const executeBatchPublishStatus = async () => {
  const selectedFiles = selectedItems.value.filter(f => !f.is_directory && f.md5_hash)

  if (selectedFiles.length === 0) {
    ElMessage.warning('没有选中有MD5记录的文件')
    return
  }

  if (!batchPublishStatusForm.platform) {
    ElMessage.warning('请输入发布平台')
    return
  }

  batchPublishStatusLoading.value = true

  try {
    let successCount = 0
    let errorCount = 0
    const errors: string[] = []

    // 为每个选中的文件设置发布状态
    for (const file of selectedFiles) {
      try {
        const platformData = {
          platform: batchPublishStatusForm.platform,
          is_published: batchPublishStatusForm.is_published,
          publish_date: batchPublishStatusForm.publish_date || undefined,
          publish_account: batchPublishStatusForm.account || undefined,
          video_id: batchPublishStatusForm.video_id || undefined,
          video_url: batchPublishStatusForm.video_url || undefined,
          notes: batchPublishStatusForm.notes || undefined
        }

        if (file.md5_hash) {
          await updatePlatformPublishStatus(file.md5_hash, batchPublishStatusForm.platform, platformData)
        } else {
          throw new Error('文件没有MD5记录')
        }
        successCount++
      } catch (error: any) {
        errorCount++
        errors.push(`${file.name}: ${error.message || error}`)
        console.error(`设置文件 ${file.name} 发布状态失败:`, error)
      }
    }

    // 显示结果
    if (successCount > 0) {
      ElMessage.success(`成功设置 ${successCount} 个文件的发布状态`)
    }

    if (errorCount > 0) {
      console.warn('批量设置发布状态过程中的错误:', errors)
      ElMessage.warning(`有 ${errorCount} 个文件设置失败，请查看控制台`)
    }

    // 关闭对话框并刷新
    showBatchPublishStatusDialog.value = false
    selectedItems.value = []  // 清空选中项
    await loadFolderMD5Records()  // 重新加载MD5记录缓存，更新状态显示

  } catch (error: any) {
    console.error('批量设置发布状态失败:', error)
    ElMessage.error(`批量设置发布状态失败: ${error.message || error}`)
  } finally {
    batchPublishStatusLoading.value = false
  }
}

// 打开归档已发布文件对话框
const openArchivePublishedDialog = () => {
  // 自动填入当前路径检测到的平台
  const pathInfo = getCurrentPathInfoSync()
  if (pathInfo.platform !== 'unknown') {
    archivePublishedForm.platforms = [pathInfo.platform]
  } else {
    archivePublishedForm.platforms = []
  }

  archivePublishedForm.archiveFolderName = '已发布'
  showArchivePublishedDialog.value = true
}

// 执行归档已发布文件
const executeArchivePublished = async () => {
  if (!archivePublishedForm.archiveFolderName.trim()) {
    ElMessage.warning('请输入归档文件夹名称')
    return
  }

  archivePublishedLoading.value = true

  try {
    const requestData = {
      folder_path: currentFolderPath.value,
      archive_folder_name: archivePublishedForm.archiveFolderName,
      platforms: archivePublishedForm.platforms.length > 0 ? archivePublishedForm.platforms : undefined
    }

    const response = await archivePublishedFiles(requestData)

    if (response.data.success) {
      const { archived_count, skipped_count, archived_files, errors } = response.data

      if (archived_count > 0) {
        ElMessage.success(`成功归档 ${archived_count} 个已发布文件`)

        // 显示归档的文件列表
        if (archived_files.length > 0) {
          console.log('归档的文件:', archived_files)
          ElNotification({
            title: '归档完成',
            message: `已归档文件：${archived_files.slice(0, 5).join(', ')}${archived_files.length > 5 ? '...' : ''}`,
            type: 'success',
            duration: 5000
          })
        }
      } else {
        ElMessage.info(`没有找到已发布的文件需要归档（跳过 ${skipped_count} 个文件）`)
      }

      if (errors.length > 0) {
        console.warn('归档过程中的错误:', errors)
        ElMessage.warning(`有 ${errors.length} 个文件归档失败，请查看控制台`)
      }

      // 关闭对话框并刷新文件列表
      showArchivePublishedDialog.value = false
      await loadFileList()  // 刷新文件列表

    } else {
      ElMessage.error('归档操作失败')
    }

  } catch (error: any) {
    console.error('归档已发布文件失败:', error)
    ElMessage.error(`归档操作失败: ${error.message || error}`)
  } finally {
    archivePublishedLoading.value = false
  }
}

const executeBatchRename = async () => {
  if (!batchRenameForm.fileNames.trim()) {
    ElMessage.warning('请输入新的文件名列表')
    return
  }

  const fileNames = batchRenameForm.fileNames
    .split('\n')
    .map(name => name.trim())
    .filter(name => name.length > 0)

  if (fileNames.length === 0) {
    ElMessage.warning('请输入有效的文件名')
    return
  }

  // 检查文件名数量是否匹配
  const targetFileCount = getTargetFileCount()
  if (fileNames.length !== targetFileCount) {
    ElMessage.warning(`文件名数量不匹配：需要 ${targetFileCount} 个文件名，但输入了 ${fileNames.length} 个`)
    return
  }

  batchRenameLoading.value = true
  try {
    // 构建请求参数
    const requestData: any = {
      folder_path: currentFolderPath.value,
      file_names: fileNames
    }

    // 如果是选中文件模式，传递选中的文件列表
    if (batchRenameForm.mode === 'selected') {
      const selectedFiles = selectedItems.value
        .filter(f => !f.is_directory)
        .map(f => f.name)
        .sort() // 确保顺序一致

      if (selectedFiles.length === 0) {
        ElMessage.warning('没有选中的文件可以重命名')
        return
      }

      requestData.selected_files = selectedFiles
    }

    const response = await batchRenameFiles(requestData)

    if (response.data.success) {
      ElMessage.success(`成功重命名 ${response.data.renamed_count} 个文件`)
      if (response.data.errors.length > 0) {
        console.warn('重命名过程中的错误:', response.data.errors)
        ElMessage.warning(`有 ${response.data.errors.length} 个文件重命名失败，请查看控制台`)
      }

      showBatchRenameDialog.value = false
      batchRenameForm.fileNames = ''
      selectedItems.value = []  // 清空选中项
      loadFileList()  // 刷新文件列表
    } else {
      ElMessage.error('批量重命名失败')
    }
  } catch (error: any) {
    console.error('批量重命名失败:', error)
    ElMessage.error(`批量重命名失败: ${error.message || error}`)
  } finally {
    batchRenameLoading.value = false
  }
}

// 视频合并相关方法
const getVideoFileCount = () => {
  const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v']
  const videoFiles = fileList.value.filter(file =>
    !file.is_directory &&
    file.extension &&
    videoExtensions.includes(file.extension.toLowerCase())
  )
  console.log('检测到的视频文件:', videoFiles.map(f => f.name))
  return videoFiles.length
}

const openVideoMergeDialog = () => {
  console.log('openVideoMergeDialog 被调用')
  const videoCount = getVideoFileCount()
  console.log('当前视频文件数量:', videoCount)

  if (videoCount === 0) {
    ElMessage.warning('当前文件夹中没有找到视频文件')
    return
  }

  if (videoCount < 2) {
    ElMessage.warning('至少需要2个视频文件才能进行合并')
    return
  }

  console.log('打开视频合并对话框')
  showVideoMergeDialog.value = true
}

const startVideoMerge = async () => {
  // 验证参数
  if (videoMergeForm.targetDurationMin >= videoMergeForm.targetDurationMax) {
    ElMessage.error('最小时长必须小于最大时长')
    return
  }

  if (videoMergeForm.targetDurationMin < 10) {
    ElMessage.error('最小时长不能少于10秒')
    return
  }

  // 清空之前的进度状态
  videoMergeProgress.value = null
  videoMergeTaskId.value = ''

  videoMergeLoading.value = true

  try {
    const request: VideoMergeRequest = {
      folder_path: currentFolderPath.value,
      target_duration_min: videoMergeForm.targetDurationMin,
      target_duration_max: videoMergeForm.targetDurationMax,
      enable_transitions: videoMergeForm.enableTransitions,
      output_quality: videoMergeForm.outputQuality,
      max_videos_per_merge: videoMergeForm.maxVideosPerMerge
    }

    console.log('创建视频合并任务:', request)

    const response = await createVideoMergeTask(request)
    console.log('视频合并任务创建响应:', response)

    if (response.data.success) {
      videoMergeTaskId.value = response.data.task_id
      showVideoMergeDialog.value = false
      showVideoMergeProgressDialog.value = true

      ElMessage.success('视频合并任务已创建，正在后台处理...')

      // 开始轮询任务进度
      startProgressPolling()
    } else {
      ElMessage.error('创建视频合并任务失败')
    }
  } catch (error: any) {
    console.error('创建视频合并任务失败:', error)
    ElMessage.error(`创建视频合并任务失败: ${error.message || error}`)
  } finally {
    videoMergeLoading.value = false
  }
}

const startProgressPolling = () => {
  const pollInterval = setInterval(async () => {
    try {
      if (!videoMergeTaskId.value) {
        clearInterval(pollInterval)
        return
      }

      const response = await getVideoMergeProgress(videoMergeTaskId.value)
      videoMergeProgress.value = response.data

      // 如果任务完成或失败，停止轮询
      if (response.data.status === 'completed' ||
          response.data.status === 'failed' ||
          response.data.status === 'cancelled') {
        clearInterval(pollInterval)

        if (response.data.status === 'completed') {
          ElMessage.success('视频合并完成！')
          // 刷新文件列表以显示新的output文件夹
          loadFileList()
        } else if (response.data.status === 'failed') {
          ElMessage.error('视频合并失败')
        }
      }
    } catch (error) {
      console.error('获取视频合并进度失败:', error)
      clearInterval(pollInterval)
    }
  }, 2000) // 每2秒轮询一次
}

const cancelVideoMerge = async () => {
  try {
    await cancelVideoMergeTask(videoMergeTaskId.value)
    ElMessage.success('任务已取消')
    showVideoMergeProgressDialog.value = false
  } catch (error: any) {
    console.error('取消任务失败:', error)
    ElMessage.error(`取消任务失败: ${error.message || error}`)
  }
}

const openOutputFolder = () => {
  const outputPath = `${currentFolderPath.value}\\output`
  currentFolderPath.value = outputPath
  loadFileList()
  showVideoMergeProgressDialog.value = false
  ElMessage.success('已切换到输出文件夹')
}

const getMergeProgressStep = () => {
  if (!videoMergeProgress.value) return 0

  const status = videoMergeProgress.value.status
  const progress = videoMergeProgress.value.progress

  if (status === 'pending') return 0
  if (progress < 20) return 1
  if (progress < 90) return 2
  return 3
}

const getProgressStatus = () => {
  if (!videoMergeProgress.value) return undefined

  const status = videoMergeProgress.value.status
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return undefined
}

const getStatusTagType = (status: string) => {
  switch (status) {
    case 'pending': return 'info'
    case 'processing': return 'warning'
    case 'completed': return 'success'
    case 'failed': return 'danger'
    case 'cancelled': return 'info'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '等待中'
    case 'processing': return '处理中'
    case 'completed': return '已完成'
    case 'failed': return '失败'
    case 'cancelled': return '已取消'
    default: return '未知'
  }
}

const formatDurationFromSeconds = (seconds: number) => {
  if (!seconds || seconds <= 0) return '-'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
}

// 判断是否为长视频（超过35秒）
const isLongVideo = (file: any) => {
  return !file.is_directory &&
         isVideoFile(file) &&
         file.media_info?.duration &&
         file.media_info.duration > 35
}

const getVideoResolution = (mediaInfo: any) => {
  if (!mediaInfo?.video) return '-'
  const { width, height } = mediaInfo.video
  if (!width || !height) return '-'
  return `${width}x${height}`
}

const getVideoBitrate = (mediaInfo: any) => {
  if (!mediaInfo?.bit_rate) return '-'
  const bitrate = mediaInfo.bit_rate
  if (bitrate > 1000000) {
    return `${(bitrate / 1000000).toFixed(1)} Mbps`
  } else if (bitrate > 1000) {
    return `${(bitrate / 1000).toFixed(1)} Kbps`
  }
  return `${bitrate} bps`
}

const copyToClipboard = async (text: string, showMessage: boolean = true) => {
  try {
    // 优先使用现代 Clipboard API
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(text)
      if (showMessage) {
        ElMessage.success('已复制到剪贴板')
      }
      return
    }

    // 降级方案：使用传统的 document.execCommand
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()

    const successful = document.execCommand('copy')
    document.body.removeChild(textArea)

    if (successful) {
      if (showMessage) {
        ElMessage.success('已复制到剪贴板')
      }
    } else {
      throw new Error('execCommand failed')
    }
  } catch (error) {
    console.error('复制失败:', error)
    if (showMessage) {
      ElMessage.error(`复制失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
    throw error // 重新抛出错误，让调用方处理
  }
}

// ==================== 三拼视频相关方法 ====================

// 获取竖版视频数量
const getVerticalVideoCount = () => {
  const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v']
  const videoFiles = fileList.value.filter(file =>
    !file.is_directory &&
    file.extension &&
    videoExtensions.includes(file.extension.toLowerCase())
  )

  // 这里简化处理，实际应该检查视频的宽高比
  // 暂时返回所有视频文件数量，后续可以通过媒体信息判断
  return videoFiles.length
}

// 打开三拼视频对话框
const openTripleVideoDialog = () => {
  console.log('openTripleVideoDialog 被调用')
  const verticalVideoCount = getVerticalVideoCount()
  console.log('当前竖版视频文件数量:', verticalVideoCount)

  if (verticalVideoCount === 0) {
    ElMessage.warning('当前文件夹中没有找到视频文件')
    return
  }

  if (verticalVideoCount < 3) {
    ElMessage.warning('至少需要3个竖版视频才能制作三拼视频')
    return
  }

  console.log('打开三拼视频对话框')
  showTripleVideoDialog.value = true
}

// 开始三拼视频制作
const startTripleVideo = async () => {
  // 清空之前的进度状态
  tripleVideoProgress.value = null
  tripleVideoTaskId.value = ''

  tripleVideoLoading.value = true

  try {
    const request: TripleVideoMergeRequest = {
      folder_path: currentFolderPath.value,
      output_quality: tripleVideoForm.outputQuality,
      video_duration_per_segment: 0, // 使用视频实际时长，此参数将被忽略
      transition_duration: tripleVideoForm.transitionDuration,
      enable_preview: tripleVideoForm.enablePreview
    }

    console.log('创建三拼视频任务:', request)

    const response = await createTripleVideoTask(request)
    console.log('三拼视频任务创建响应:', response)

    if (response.data.success) {
      tripleVideoTaskId.value = response.data.task_id
      showTripleVideoDialog.value = false
      showTripleVideoProgressDialog.value = true

      ElMessage.success('三拼视频任务已创建，正在后台处理...')

      // 开始轮询任务进度
      startTripleProgressPolling()
    } else {
      ElMessage.error('创建三拼视频任务失败')
    }
  } catch (error: any) {
    console.error('创建三拼视频任务失败:', error)
    ElMessage.error(`创建三拼视频任务失败: ${error.message || error}`)
  } finally {
    tripleVideoLoading.value = false
  }
}

// 开始三拼视频进度轮询
const startTripleProgressPolling = () => {
  const pollInterval = setInterval(async () => {
    try {
      if (!tripleVideoTaskId.value) {
        clearInterval(pollInterval)
        return
      }

      const response = await getTripleVideoProgress(tripleVideoTaskId.value)
      tripleVideoProgress.value = response.data

      // 如果任务完成或失败，停止轮询
      if (response.data.status === 'completed' ||
          response.data.status === 'failed' ||
          response.data.status === 'cancelled') {
        clearInterval(pollInterval)

        if (response.data.status === 'completed') {
          ElMessage.success('三拼视频制作完成！')
          // 刷新文件列表以显示新的triple_output文件夹
          loadFileList()
        } else if (response.data.status === 'failed') {
          ElMessage.error('三拼视频制作失败')
        }
      }
    } catch (error) {
      console.error('获取三拼视频进度失败:', error)
      clearInterval(pollInterval)
    }
  }, 2000) // 每2秒轮询一次
}

// 取消三拼视频任务
const cancelTripleVideo = async () => {
  try {
    await cancelTripleVideoTask(tripleVideoTaskId.value)
    ElMessage.success('任务已取消')
    showTripleVideoProgressDialog.value = false
  } catch (error: any) {
    console.error('取消任务失败:', error)
    ElMessage.error(`取消任务失败: ${error.message || error}`)
  }
}

// 打开三拼视频输出文件夹
const openTripleOutputFolder = () => {
  const outputPath = `${currentFolderPath.value}\\triple_output`
  currentFolderPath.value = outputPath
  loadFileList()
  showTripleVideoProgressDialog.value = false
  ElMessage.success('已切换到三拼视频输出文件夹')
}

// 获取三拼视频进度步骤
const getTripleProgressStep = () => {
  if (!tripleVideoProgress.value) return 0

  const status = tripleVideoProgress.value.status
  const progress = tripleVideoProgress.value.progress

  if (status === 'pending') return 0
  if (progress < 30) return 1
  if (progress < 90) return 2
  return 3
}

// 获取三拼视频进度状态
const getTripleProgressStatus = () => {
  if (!tripleVideoProgress.value) return undefined

  const status = tripleVideoProgress.value.status
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return undefined
}

// 去水印相关方法
const isVideoFile = (file: FileItem) => {
  const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v']
  return file.extension && videoExtensions.includes(file.extension.toLowerCase())
}

const openWatermarkProcessDialog = () => {
  const videoCount = getVideoFileCount()
  if (videoCount === 0) {
    ElMessage.warning('当前文件夹中没有找到视频文件')
    return
  }

  // 设置默认值
  watermarkForm.inputFolder = currentFolderPath.value
  watermarkForm.outputFolder = `${currentFolderPath.value}\\cleaned`

  showWatermarkDialog.value = true
}

const processVideoWatermark = (file: FileItem) => {
  if (!isVideoFile(file)) {
    ElMessage.warning('只能对视频文件进行去水印处理')
    return
  }

  // 设置单文件处理模式
  watermarkForm.processMode = 'single'
  watermarkForm.inputFile = file.path
  watermarkForm.outputFile = file.path.replace(/(\.[^.]+)$/, '_no_watermark$1')

  showWatermarkDialog.value = true
}

const startWatermarkProcessing = async () => {
  try {
    watermarkProcessing.value = true

    // 构建请求数据
    const requestData: any = {
      detection_config: {
        detection_mode: watermarkForm.detectionMode,
        sensitivity: watermarkForm.sensitivity,
        save_detection_result: false
      },
      removal_config: {
        removal_mode: watermarkForm.removalMode,
        inpaint_method: watermarkForm.inpaintMethod,
        output_quality: watermarkForm.outputQuality,
        preserve_encoding: false
      }
    }

    // 添加检测相关配置
    if (watermarkForm.detectionMode === 'template' && watermarkForm.templatePath) {
      requestData.detection_config.template_path = watermarkForm.templatePath
    }

    if (watermarkForm.detectionMode === 'region' && watermarkForm.detectionRegion) {
      requestData.detection_config.detection_region = watermarkForm.detectionRegion
    }

    // 添加清除相关配置
    if (watermarkForm.removalMode === 'manual' && watermarkForm.watermarkRegions) {
      requestData.removal_config.watermark_regions = watermarkForm.watermarkRegions
        .split('\n')
        .filter(line => line.trim())
        .map(line => line.trim())
    }

    let response: any

    if (watermarkForm.processMode === 'single') {
      // 单文件处理
      const singleRequestData = {
        input_video_path: watermarkForm.inputFile,
        output_video_path: watermarkForm.outputFile || watermarkForm.inputFile.replace(/(\.[^.]+)$/, '_no_watermark$1'),
        ...requestData.removal_config
      }

      response = await fetch('/api/v1/filesystem/watermark/remove', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(singleRequestData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      if (result.success) {
        ElMessage.success('视频去水印处理完成')
        showWatermarkDialog.value = false
        loadFileList() // 刷新文件列表
      } else {
        throw new Error(result.error || '处理失败')
      }

    } else {
      // 批量处理
      const batchRequestData = {
        input_folder_path: watermarkForm.inputFolder,
        output_folder_path: watermarkForm.outputFolder || `${watermarkForm.inputFolder}\\cleaned`,
        process_mode: 'detect_and_remove',
        file_filters: watermarkForm.fileFilters,
        recursive: watermarkForm.recursive,
        max_concurrent: watermarkForm.maxConcurrent,
        detection_config: requestData.detection_config,
        removal_config: requestData.removal_config
      }

      response = await fetch('/api/v1/filesystem/watermark/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(batchRequestData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      if (result.success) {
        watermarkTaskId.value = result.task_id
        showWatermarkDialog.value = false
        showWatermarkProgressDialog.value = true
        startWatermarkProgressPolling()
        ElMessage.success('批量去水印任务已启动')
      } else {
        throw new Error(result.error || '任务创建失败')
      }
    }

  } catch (error: any) {
    console.error('去水印处理失败:', error)
    ElMessage.error(`去水印处理失败: ${error.message}`)
  } finally {
    watermarkProcessing.value = false
  }
}

const startWatermarkProgressPolling = () => {
  const pollInterval = setInterval(async () => {
    try {
      if (!watermarkTaskId.value) {
        clearInterval(pollInterval)
        return
      }

      const response = await fetch(`/api/v1/filesystem/watermark/batch/${watermarkTaskId.value}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (response.ok) {
        watermarkProgress.value = await response.json()

        if (watermarkProgress.value.status === 'completed' || watermarkProgress.value.status === 'failed') {
          clearInterval(pollInterval)
          if (watermarkProgress.value.status === 'completed') {
            ElMessage.success('批量去水印处理完成')
            loadFileList() // 刷新文件列表
          }
        }
      }
    } catch (error) {
      console.error('获取去水印进度失败:', error)
      clearInterval(pollInterval)
    }
  }, 2000)
}

const cancelWatermarkProcessing = async () => {
  try {
    const response = await fetch(`/api/v1/filesystem/watermark/batch/${watermarkTaskId.value}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    if (response.ok) {
      ElMessage.success('任务已取消')
      showWatermarkProgressDialog.value = false
    }
  } catch (error: any) {
    ElMessage.error(`取消任务失败: ${error.message}`)
  }
}

const openWatermarkOutputFolder = () => {
  const outputPath = watermarkForm.outputFolder || `${currentFolderPath.value}\\cleaned`
  currentFolderPath.value = outputPath
  loadFileList()
  showWatermarkProgressDialog.value = false
  ElMessage.success('已切换到去水印输出文件夹')
}

const getWatermarkProgressStep = () => {
  if (!watermarkProgress.value) return 0

  const status = watermarkProgress.value.status
  if (status === 'processing') return 1
  if (status === 'completed') return 2
  return 0
}

const getWatermarkProgressStatus = () => {
  if (!watermarkProgress.value) return undefined

  const status = watermarkProgress.value.status
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return undefined
}

const formatSensitivity = (value: number) => {
  if (value <= 0.3) return '低敏感度'
  if (value <= 0.7) return '中等敏感度'
  return '高敏感度'
}

const getFileName = (filePath: string) => {
  return filePath.split(/[/\\]/).pop() || filePath
}



// 对标账号下载相关方法
const loadBenchmarkAccounts = async () => {
  loadingBenchmarks.value = true
  try {
    // 调用真实的对标账号API
    const response = await fetch('/api/v1/benchmark/accounts', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    benchmarkAccounts.value = data.items || []

  } catch (error) {
    console.error('加载对标账号失败:', error)
    ElMessage.error('加载对标账号失败')

    // 清空列表
    benchmarkAccounts.value = []
  } finally {
    loadingBenchmarks.value = false
  }
}

const handleBenchmarkSelection = (selection: BenchmarkAccount[]) => {
  selectedBenchmarkAccounts.value = selection
}

const nextDownloadStep = () => {
  if (downloadStep.value === 0 && selectedBenchmarkAccounts.value.length === 0) {
    ElMessage.warning('请至少选择一个对标账号')
    return
  }

  if (downloadStep.value === 1 && downloadConfig.contentTypes.length === 0) {
    ElMessage.warning('请至少选择一种内容类型')
    return
  }

  downloadStep.value++
}

const startBenchmarkDownload = async () => {
  downloadingBenchmark.value = true
  try {
    // 构建下载任务数据
    const downloadTasks = selectedBenchmarkAccounts.value.map(account => {
      const currentMonth = new Date().toISOString().slice(0, 7) // YYYY-MM

      // 构建正确的下载路径
      let downloadPath = ''
      const ourAccountInfo = account.our_account_info
      if (ourAccountInfo && ourAccountInfo.status !== 'not_found') {
        // 获取关联账号的平台名称和账号名称
        const ourPlatformName = getPlatformDisplayName(ourAccountInfo.platform_id)
        const ourAccountName = formatOurAccountName(ourAccountInfo)
        downloadPath = `${downloadConfig.basePath}${ourPlatformName}/${ourAccountName}/${account.account_name}/${currentMonth}/`
      } else {
        // 如果没有关联账号信息，使用默认结构
        downloadPath = `${downloadConfig.basePath}未关联平台/未关联账号/${account.account_name}/${currentMonth}/`
      }

      return {
        account_id: account._id,
        account_name: account.account_name,
        platform: account.platform,
        download_path: downloadPath,
        content_types: downloadConfig.contentTypes,
        max_count: downloadConfig.maxCount,
        time_range: downloadConfig.timeRange,
        download_mode: downloadConfig.downloadMode,
        naming_rule: downloadConfig.namingRule,
        filters: {
          min_views: downloadConfig.minViews ? parseInt(downloadConfig.minViews) : null,
          min_likes: downloadConfig.minLikes ? parseInt(downloadConfig.minLikes) : null,
          keywords: downloadConfig.keywords ? downloadConfig.keywords.split(',').map(k => k.trim()) : []
        }
      }
    })

    console.log('创建对标账号下载任务:', downloadTasks)

    // 调用真实的下载API
    const response = await fetch('/api/v1/benchmark/download/tasks', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({ tasks: downloadTasks })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    ElMessage.success(result.message || `成功创建 ${downloadTasks.length} 个下载任务`)
    showBenchmarkDownloadDialog.value = false

    // 重置状态
    downloadStep.value = 0
    selectedBenchmarkAccounts.value = []

    // 刷新文件列表
    loadFileList()

  } catch (error) {
    console.error('创建下载任务失败:', error)
    ElMessage.error(`创建下载任务失败: ${error.message}`)
  } finally {
    downloadingBenchmark.value = false
  }
}

// 监听对话框打开事件
watch(showBenchmarkDownloadDialog, (newVal) => {
  if (newVal) {
    loadBenchmarkAccounts()
    downloadStep.value = 0
    selectedBenchmarkAccounts.value = []
  }
})

// ==================== MD5记录管理方法 ====================

// 平台显示名称映射（用于MD5记录管理）
const md5PlatformDisplayNames: Record<string, string> = {
  youtube: 'YouTube',
  tiktok: 'TikTok',
  instagram: 'Instagram',
  weibo: '微博',
  douyin: '抖音',
  kuaishou: '快手',
  xiaohongshu: '小红书',
  bilibili: 'B站',
  other: '其他'
}

// 获取MD5记录管理中的平台显示名称
const getMD5PlatformDisplayName = (platform: string) => {
  return md5PlatformDisplayNames[platform] || platform
}

// 获取已发布平台数量
const getPublishedPlatformCount = (platformRecords: PlatformPublishRecord[]) => {
  if (!platformRecords) return 0
  return platformRecords.filter(p => p.is_published).length
}

// ==================== 文件MD5记录状态相关方法 ====================

// 获取文件的MD5记录状态
const getFileMD5RecordStatus = (file: FileItem) => {
  // 首先尝试通过MD5哈希值查找
  if (file.md5_hash) {
    const hasRecord = fileMD5RecordsCache.value.has(file.md5_hash)
    console.log(`🔍 检查文件 ${file.name} MD5状态: ${file.md5_hash} -> ${hasRecord ? '已记录' : '未记录'}`)
    console.log(`📦 当前缓存大小: ${fileMD5RecordsCache.value.size}`)
    return hasRecord
  }

  // 如果没有MD5哈希值，尝试通过文件名查找
  console.log(`⚠️ 文件 ${file.name} 没有MD5哈希值，尝试通过文件名匹配`)

  // 遍历缓存中的记录，查找匹配的文件名
  for (const [md5Hash, record] of fileMD5RecordsCache.value.entries()) {
    if (record.file_name === file.name) {
      console.log(`✅ 通过文件名找到匹配记录: ${file.name} -> ${md5Hash}`)
      return true
    }
  }

  console.log(`❌ 文件 ${file.name} 没有找到匹配的MD5记录`)
  return false
}

// 获取文件的MD5记录
const getFileMD5Record = (file: FileItem): VideoMD5Record | null => {
  // 首先尝试通过MD5哈希值查找
  if (file.md5_hash) {
    return fileMD5RecordsCache.value.get(file.md5_hash) || null
  }

  // 如果没有MD5哈希值，尝试通过文件名查找
  for (const [md5Hash, record] of fileMD5RecordsCache.value.entries()) {
    if (record.file_name === file.name) {
      return record
    }
  }

  return null
}

// 获取文件已发布的平台数量
const getFilePublishedPlatformCount = (file: FileItem) => {
  const record = getFileMD5Record(file)
  if (!record || !record.platform_records) return 0
  return record.platform_records.filter(p => p.is_published).length
}

// 获取文件总平台数量
const getFileTotalPlatformCount = (file: FileItem) => {
  const record = getFileMD5Record(file)
  if (!record || !record.platform_records) return 0
  return record.platform_records.length
}

// 获取文件已发布的平台列表
const getFilePublishedPlatforms = (file: FileItem) => {
  const record = getFileMD5Record(file)
  if (!record || !record.platform_records) return []
  return record.platform_records
    .filter(p => p.is_published)
    .map(p => p.platform)
}

// 批量加载当前文件夹的MD5记录
const loadFolderMD5Records = async () => {
  try {
    console.log('🔍 开始加载文件夹MD5记录缓存...')
    console.log(`📂 当前文件列表总数: ${fileList.value.length}`)
    console.log(`📂 当前文件夹路径: ${currentFolderPath.value}`)

    // 调试：输出前几个文件的详细信息
    console.log('📋 前3个文件的详细信息:')
    fileList.value.slice(0, 3).forEach((file, index) => {
      console.log(`  ${index + 1}. 文件名: ${file.name}`)
      console.log(`     是否目录: ${file.is_directory}`)
      console.log(`     扩展名: ${file.extension}`)
      console.log(`     MD5哈希: ${file.md5_hash}`)
      console.log(`     文件大小: ${file.size}`)

      // 如果扩展名为null，尝试从文件名提取
      if (!file.extension && file.name.includes('.')) {
        const extractedExt = file.name.split('.').pop()?.toLowerCase()
        console.log(`     从文件名提取的扩展名: ${extractedExt}`)
      }
    })

    // 收集当前文件夹中所有视频文件的MD5哈希值
    const videoFiles = fileList.value.filter(file => {
      if (file.is_directory || !file.md5_hash) return false

      // 获取文件扩展名，优先使用file.extension，如果为null则从文件名提取
      let extension = file.extension
      if (!extension && file.name.includes('.')) {
        extension = file.name.split('.').pop()?.toLowerCase() || ''
      }

      return ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v'].includes(extension || '')
    })

    console.log(`📁 当前文件夹中有 ${videoFiles.length} 个视频文件`)

    // 调试：如果没有视频文件，分析原因
    if (videoFiles.length === 0) {
      const allFiles = fileList.value.filter(file => !file.is_directory)
      console.log(`📄 非目录文件总数: ${allFiles.length}`)

      const filesWithMD5 = allFiles.filter(file => file.md5_hash)
      console.log(`🔐 有MD5哈希的文件数: ${filesWithMD5.length}`)

      const videoExtFiles = allFiles.filter(file => {
        // 获取文件扩展名，优先使用file.extension，如果为null则从文件名提取
        let extension = file.extension
        if (!extension && file.name.includes('.')) {
          extension = file.name.split('.').pop()?.toLowerCase() || ''
        }
        return ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v'].includes(extension || '')
      })
      console.log(`🎬 视频扩展名文件数: ${videoExtFiles.length}`)

      // 输出一些示例文件的扩展名
      console.log('📝 前5个文件的扩展名:')
      allFiles.slice(0, 5).forEach((file, index) => {
        console.log(`  ${index + 1}. ${file.name} -> 扩展名: "${file.extension}"`)
      })

      // 如果有视频文件但没有MD5，尝试通过文件名查询
      if (videoExtFiles.length > 0) {
        console.log('🔄 尝试通过文件名查询MD5记录...')
        console.log(`📂 当前文件夹路径: ${currentFolderPath.value}`)

        // 智能检测路径：如果当前路径看起来包含文件名前缀，则使用父目录
        let queryFolderPath = currentFolderPath.value

        const pathSegments = currentFolderPath.value.split('\\')
        const lastSegment = pathSegments[pathSegments.length - 1]

        // 检测条件：
        // 1. 最后一段路径很长（>50字符）
        // 2. 包含特殊字符（如日文、中文、特殊符号）
        // 3. 当前文件夹中有视频文件但路径看起来像文件名
        if (lastSegment.length > 50 && /[^\w\-\s]/.test(lastSegment)) {
          const parentPath = pathSegments.slice(0, -1).join('\\')
          console.log(`🔍 检测到可能的文件名前缀路径，尝试使用父目录查询:`)
          console.log(`   原路径: ${currentFolderPath.value}`)
          console.log(`   父路径: ${parentPath}`)
          queryFolderPath = parentPath
        }

        try {
          // 获取文件夹的MD5记录
          const response = await getVideoMD5Records({
            page: 1,
            limit: 1000,
            folder_path: queryFolderPath
          })

          if (response.data && response.data.records) {
            console.log(`📋 从数据库查询到 ${response.data.records.length} 条MD5记录`)

            // 清空缓存
            fileMD5RecordsCache.value.clear()

            // 通过文件名匹配MD5记录
            response.data.records.forEach((record: VideoMD5Record) => {
              fileMD5RecordsCache.value.set(record.md5_hash, record)
              console.log(`💾 缓存MD5记录: ${record.file_name} -> ${record.md5_hash}`)
            })

            console.log(`🎯 通过文件名匹配完成，共 ${fileMD5RecordsCache.value.size} 条记录`)
            return
          }
        } catch (error) {
          console.error('❌ 通过文件名查询MD5记录失败:', error)
        }
      }
    }

    const md5Hashes = videoFiles.map(file => file.md5_hash).filter(Boolean) as string[]

    console.log(`🔐 收集到 ${md5Hashes.length} 个MD5哈希值:`, md5Hashes.slice(0, 3))

    if (md5Hashes.length === 0) {
      console.log('⚠️ 没有MD5哈希值，清空缓存')
      fileMD5RecordsCache.value.clear()
      return
    }

    // 批量查询这些MD5哈希值对应的记录
    console.log('🌐 调用批量查询API...')
    const response = await batchGetMD5Records(md5Hashes)
    console.log('📡 批量查询API响应:', response)

    // 清空缓存
    fileMD5RecordsCache.value.clear()

    // 处理批量查询结果
    if (response.data && response.data.success && response.data.records) {
      console.log(`✅ 找到 ${response.data.records.length} 条MD5记录`)
      response.data.records.forEach((record: VideoMD5Record) => {
        fileMD5RecordsCache.value.set(record.md5_hash, record)
        console.log(`💾 缓存MD5记录: ${record.file_name} -> ${record.md5_hash}`)
      })
      console.log(`🎯 缓存更新完成，共 ${fileMD5RecordsCache.value.size} 条记录`)
    } else {
      console.log('❌ 批量查询返回空结果或失败')
    }

  } catch (error) {
    console.error('❌ 加载文件夹MD5记录失败:', error)
    console.error('❌ 错误详情:', error.message)
    console.error('❌ 错误堆栈:', error.stack)
    // 静默失败，不影响用户体验
  }
}



// 打开MD5管理对话框
const openMD5ManagementDialog = () => {
  showMD5ManagementDialog.value = true
  loadMD5Records()
}

// 加载MD5记录列表
const loadMD5Records = async () => {
  try {
    md5Loading.value = true
    const response = await getVideoMD5Records({
      page: md5CurrentPage.value,
      limit: md5PageSize.value,
      folder_path: currentFolderPath.value
    })

    md5Records.value = response.data.records
    md5TotalCount.value = response.data.total

  } catch (error) {
    console.error('加载MD5记录失败:', error)
    ElMessage.error('加载MD5记录失败')
  } finally {
    md5Loading.value = false
  }
}

// 批量保存当前文件夹的MD5记录
const batchSaveMD5Records = async () => {
  try {
    md5Loading.value = true
    const response = await batchSaveFolderMD5Records(currentFolderPath.value)

    if (response.data.success) {
      ElMessage.success(response.data.message)
      loadMD5Records() // 重新加载记录
      await loadFolderMD5Records() // 重新加载文件夹MD5记录缓存，更新文件状态显示

      // 强制刷新界面
      await nextTick()
      console.log('🔄 批量保存完成，强制刷新界面')
    } else {
      ElMessage.warning(response.data.message)
    }

  } catch (error) {
    console.error('批量保存MD5记录失败:', error)
    ElMessage.error('批量保存MD5记录失败')
  } finally {
    md5Loading.value = false
  }
}

// MD5比对检查
const compareMD5Records = async () => {
  try {
    md5Loading.value = true
    const response = await compareFolderMD5Records(currentFolderPath.value)

    md5CompareResults.value = response.data
    showMD5CompareDialog.value = true

    if (response.data.duplicate_count > 0) {
      ElMessage.warning(`发现 ${response.data.duplicate_count} 个重复文件`)
    } else {
      ElMessage.success('未发现重复文件')
    }

  } catch (error) {
    console.error('MD5比对失败:', error)
    ElMessage.error('MD5比对失败')
  } finally {
    md5Loading.value = false
  }
}

// 刷新MD5记录
const refreshMD5Records = () => {
  loadMD5Records()
}

// 搜索MD5记录
const searchMD5Records = () => {
  // 这里可以实现搜索逻辑
  loadMD5Records()
}

// 处理MD5记录选择
const handleMD5Selection = (selection: VideoMD5Record[]) => {
  selectedMD5Records.value = selection
}

// 编辑MD5记录
const editMD5Record = (record: VideoMD5Record) => {
  // 填充编辑表单
  md5EditForm.file_name = record.file_name
  md5EditForm.md5_hash = record.md5_hash
  md5EditForm.platform_records = record.platform_records ? [...record.platform_records] : []
  md5EditForm.notes = record.notes || ''

  showMD5EditDialog.value = true
}

// 添加新平台
const addNewPlatform = () => {
  md5EditForm.platform_records.push({
    platform: '',
    is_published: false,
    publish_date: '',
    publish_account: '',
    video_id: '',
    video_url: '',
    notes: ''
  })
}

// 删除平台
const removePlatform = (index: number) => {
  md5EditForm.platform_records.splice(index, 1)
}

// 快速编辑文件的MD5记录
const quickEditFileMD5Record = async (file: FileItem) => {
  if (!file.md5_hash) {
    ElMessage.warning('文件没有MD5哈希值，无法管理记录')
    return
  }

  try {
    // 检查是否已有记录
    const existingRecord = getFileMD5Record(file)

    if (existingRecord) {
      // 编辑现有记录
      editMD5Record(existingRecord)
    } else {
      // 创建新记录
      const newRecord: VideoMD5RecordCreate = {
        file_path: file.path,
        file_name: file.name,
        md5_hash: file.md5_hash,
        file_size: file.size,
        duration: file.media_info?.duration ? Math.floor(file.media_info.duration) : undefined,
        resolution: file.media_info?.video ?
          `${file.media_info.video.width}x${file.media_info.video.height}` : undefined,
        notes: ''
      }

      // 保存新记录
      await createVideoMD5Record(newRecord)
      ElMessage.success('MD5记录创建成功')

      // 重新加载MD5记录缓存
      await loadFolderMD5Records()

      // 获取新创建的记录并编辑
      const createdRecord = getFileMD5Record(file)
      if (createdRecord) {
        editMD5Record(createdRecord)
      }
    }
  } catch (error) {
    console.error('快速编辑MD5记录失败:', error)
    ElMessage.error('操作失败，请重试')
  }
}



// 保存MD5记录
const saveMD5Record = async () => {
  try {
    md5Loading.value = true

    // 验证平台记录
    for (const platform of md5EditForm.platform_records) {
      if (!platform.platform) {
        ElMessage.warning('请为所有平台记录选择平台名称')
        return
      }
    }

    // 检查是否有重复的平台
    const platforms = md5EditForm.platform_records.map(p => p.platform)
    const uniquePlatforms = [...new Set(platforms)]
    if (platforms.length !== uniquePlatforms.length) {
      ElMessage.warning('不能添加重复的平台')
      return
    }

    // 更新基本记录信息
    const updateData: VideoMD5RecordUpdate = {
      notes: md5EditForm.notes || undefined
    }

    await updateVideoMD5Record(md5EditForm.md5_hash, updateData)

    // 更新各平台的发布状态
    for (const platform of md5EditForm.platform_records) {
      const platformData: PlatformPublishUpdate = {
        platform: platform.platform,
        is_published: platform.is_published,
        publish_date: platform.publish_date || undefined,
        publish_account: platform.publish_account || undefined,
        video_id: platform.video_id || undefined,
        video_url: platform.video_url || undefined,
        notes: platform.notes || undefined
      }

      await updatePlatformPublishStatus(md5EditForm.md5_hash, platform.platform, platformData)
    }

    ElMessage.success('MD5记录更新成功')
    showMD5EditDialog.value = false
    loadMD5Records() // 重新加载记录
    loadFolderMD5Records() // 重新加载文件夹MD5记录缓存

  } catch (error) {
    console.error('更新MD5记录失败:', error)
    ElMessage.error('更新MD5记录失败')
  } finally {
    md5Loading.value = false
  }
}

// 删除MD5记录
const deleteMD5Record = async (record: VideoMD5Record) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件 "${record.file_name}" 的MD5记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteVideoMD5Record(record.md5_hash)
    ElMessage.success('MD5记录删除成功')
    loadMD5Records() // 重新加载记录

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除MD5记录失败:', error)
      ElMessage.error('删除MD5记录失败')
    }
  }
}

// 批量删除MD5记录
const batchDeleteMD5Records = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedMD5Records.value.length} 条MD5记录吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    md5Loading.value = true

    for (const record of selectedMD5Records.value) {
      await deleteVideoMD5Record(record.md5_hash)
    }

    ElMessage.success(`成功删除 ${selectedMD5Records.value.length} 条MD5记录`)
    selectedMD5Records.value = []
    loadMD5Records() // 重新加载记录

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除MD5记录失败:', error)
      ElMessage.error('批量删除MD5记录失败')
    }
  } finally {
    md5Loading.value = false
  }
}

// 处理MD5分页大小变化
const handleMD5SizeChange = (newSize: number) => {
  md5PageSize.value = newSize
  md5CurrentPage.value = 1
  loadMD5Records()
}

// 处理比对结果选择
const handleCompareSelection = (selection: MD5CompareResult[]) => {
  selectedCompareResults.value = selection.filter(item => item.is_duplicate)
}

// 一键删除重复文件
const batchDeleteDuplicateFiles = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedCompareResults.value.length} 个重复文件吗？此操作不可恢复！`,
      '确认删除重复文件',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    md5Loading.value = true

    const filePaths = selectedCompareResults.value.map(item => item.file_path)
    const response = await batchDeleteFiles(filePaths)

    if (response.data.success) {
      ElMessage.success(`成功删除 ${response.data.deleted_count} 个文件`)
      showMD5CompareDialog.value = false
      loadFileList() // 重新加载文件列表
    } else {
      ElMessage.warning(`删除了 ${response.data.deleted_count} 个文件，${response.data.errors.length} 个文件删除失败`)
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除重复文件失败:', error)
      ElMessage.error('批量删除重复文件失败')
    }
  } finally {
    md5Loading.value = false
  }
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  try {
    return new Date(dateStr).toLocaleDateString('zh-CN')
  } catch {
    return dateStr
  }
}

// 生命周期
onMounted(async () => {
  // 先加载Core服务信息（用于调试）
  await loadCoreServices()
  // 加载文件列表
  await loadFileList()
  // 加载统计信息
  loadStats()
  // 初始化视频缩略图懒加载
  initIntersectionObserver()
})
</script>

<style scoped>
/* 按照现有系统风格的样式 */
.file-manager {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.management-header {
  margin-bottom: 20px;
}

.management-header h1 {
  font-size: 24px;
  color: #303133;
  margin: 0 0 8px 0;
}

.header-description {
  color: #606266;
  margin: 0;
  font-size: 14px;
}

/* 统计卡片 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  font-size: 32px;
  margin-right: 16px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.stat-card.total .stat-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-card.total .stat-number,
.stat-card.total .stat-label {
  color: white;
}

.stat-card.video .stat-content {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.stat-card.video .stat-number,
.stat-card.video .stat-label {
  color: white;
}

.stat-card.image .stat-content {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stat-card.image .stat-number,
.stat-card.image .stat-label {
  color: white;
}

.stat-card.audio .stat-content {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.stat-card.audio .stat-number,
.stat-card.audio .stat-label {
  color: white;
}

.stat-card.core-services .stat-content {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
  flex-direction: column;
  align-items: flex-start;
}

.core-services-detail {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.stat-card.recent .stat-content {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.stat-card.recent .stat-number,
.stat-card.recent .stat-label {
  color: white;
}

.stat-card.downloading .stat-content {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #303133;
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 内容列表 */
.content-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.content-info {
  padding: 8px 0;
}

.content-title {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.title-text {
  font-weight: 500;
  color: #303133;
}

.content-description {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.content-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #606266;
}

.file-info {
  font-size: 12px;
  color: #606266;
}

.file-info > div {
  margin-bottom: 4px;
}

.tags-info .category {
  margin-bottom: 4px;
}

.tags .more-tags {
  font-size: 12px;
  color: #909399;
}

.stats-info {
  font-size: 12px;
  color: #606266;
}

.stats-info .stat-item {
  margin-bottom: 4px;
}

.download-status {
  margin-top: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
}

/* 对标账号下载对话框样式 */
.benchmark-download-container {
  padding: 20px 0;
}

.step-content {
  min-height: 400px;
}

.step-header {
  text-align: center;
  margin-bottom: 30px;
}

.step-header h3 {
  font-size: 18px;
  color: #303133;
  margin: 0 0 8px 0;
}

.step-header p {
  color: #606266;
  margin: 0;
  font-size: 14px;
}

.benchmark-selection {
  margin-top: 20px;
}

.account-info .account-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.account-info .account-meta {
  display: flex;
  gap: 4px;
}

.priority-stars {
  color: #faad14;
  font-size: 14px;
}

.no-data {
  color: #c0c4cc;
  font-style: italic;
}

.download-config-form {
  margin-top: 20px;
}

.path-preview {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.path-explanation {
  margin-top: 4px;
}

.path-explanation small {
  color: #c0c4cc;
  font-size: 11px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 新增功能样式 */
.current-path {
  margin-bottom: 20px;
}

.path-info {
  display: flex;
  align-items: center;
  padding: 12px 16px;
}

.path-label {
  font-weight: 500;
  color: #303133;
  margin-right: 8px;
}

.path-value {
  color: #606266;
  font-family: monospace;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
}

.advanced-info {
  font-size: 12px;
}

.md5-info {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.md5-label {
  color: #909399;
  margin-right: 4px;
}

.md5-value {
  font-family: monospace;
  color: #303133;
  margin-right: 4px;
}

.upload-status {
  margin-bottom: 4px;
}

.media-info {
  font-size: 12px;
}

.media-info > div {
  margin-bottom: 2px;
  color: #606266;
}

.loading-text {
  color: #909399;
  font-style: italic;
}

.directory-placeholder {
  color: #c0c4cc;
  text-align: center;
}

.batch-rename-container {
  padding: 10px 0;
}

.rename-instructions {
  margin-bottom: 20px;
}

.download-summary {
  margin-top: 20px;
}

.summary-card {
  border-radius: 8px;
}

.selected-accounts h4 {
  color: #303133;
  margin: 0 0 10px 0;
  font-size: 14px;
}

.account-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-manager {
    padding: 10px;
  }

  .toolbar {
    flex-direction: column;
    gap: 12px;
  }

  .toolbar-left,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }

  .benchmark-download-container {
    padding: 10px 0;
  }

  .step-content {
    min-height: 300px;
  }

  .download-config-form .el-col {
    margin-bottom: 10px;
  }
}

/* 视频合并对话框样式 */
.video-merge-container {
  .merge-instructions {
    margin-bottom: 20px;
  }

  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 8px;
  }
}

/* 视频合并进度对话框样式 */
.merge-progress-container {
  .progress-info {
    .current-status {
      margin: 20px 0;
      text-align: center;

      h4 {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 500;
      }
    }

    .progress-details {
      margin: 20px 0;
    }

    .output-files {
      margin: 20px 0;

      h4 {
        margin-bottom: 12px;
        color: #303133;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .error-message {
      margin: 20px 0;
    }
  }
}

/* MD5记录管理样式 */
.md5-management-container {
  .md5-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background: #f5f7fa;
    border-radius: 8px;

    .toolbar-left {
      display: flex;
      gap: 12px;
    }

    .toolbar-right {
      display: flex;
      align-items: center;
    }
  }

  .md5-records-list {
    margin-bottom: 20px;
  }

  .md5-pagination {
    display: flex;
    justify-content: center;
  }
}

.file-info {
  .file-name {
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
  }

  .file-path {
    font-size: 12px;
    color: #909399;
    margin-bottom: 4px;
    font-family: monospace;
  }

  .file-meta {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: #606266;

    .file-size, .duration, .resolution {
      display: flex;
      align-items: center;
    }
  }
}

.publish-status {
  .publish-date {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }
}

.publish-info {
  font-size: 12px;

  .platform, .account {
    margin-bottom: 2px;
    color: #606266;
  }
}

.no-publish-info {
  color: #c0c4cc;
  text-align: center;
}

/* MD5比对结果样式 */
.md5-compare-container {
  .compare-summary {
    margin-bottom: 20px;
  }

  .compare-results {
    .existing-record {
      .record-info {
        .record-name {
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
        }
      }

      .record-meta {
        display: flex;
        gap: 8px;
        align-items: center;
        font-size: 12px;

        .platform {
          color: #606266;
        }
      }
    }
  }
}

/* 多平台发布状态样式 */
.platform-publish-status {
  .platform-list {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }

  .platform-item {
    .platform-tag {
      margin-bottom: 2px;
    }
  }

  .no-platform-records {
    color: #c0c4cc;
    font-size: 12px;
  }
}

.publish-stats {
  text-align: center;

  .stats-summary {
    font-size: 16px;
    font-weight: 500;
    color: #303133;

    .published-count {
      color: #67c23a;
    }

    .total-count {
      color: #909399;
    }
  }

  .stats-label {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }
}

/* 平台管理样式 */
.platform-management {
  .platform-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 6px;

    span {
      font-weight: 500;
      color: #303133;
    }
  }

  .platform-list {
    max-height: 400px;
    overflow-y: auto;
  }

  .platform-record {
    margin-bottom: 16px;

    .platform-card {
      .platform-content {
        .platform-basic {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          padding-bottom: 12px;
          border-bottom: 1px solid #ebeef5;
        }

        .platform-details {
          .el-form-item {
            margin-bottom: 12px;
          }
        }
      }
    }
  }
}

/* 简洁版MD5状态样式 */
.md5-status-simple {
  .has-md5 {
    .record-status {
      margin-bottom: 4px;
    }

    .publish-summary {
      .publish-count {
        font-size: 12px;
        color: #606266;
        font-weight: 500;
      }
    }
  }

  .no-md5-simple {
    text-align: center;
  }
}

.folder-simple {
  text-align: center;
}

/* 归档已发布文件功能样式 */
.archive-published-container {
  .path-info-card {
    margin-bottom: 20px;

    .path-details {
      .path-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          width: 120px;
          font-weight: 500;
          color: #606266;
        }

        .value {
          flex: 1;
          color: #303133;
          font-family: 'Courier New', monospace;
          font-size: 13px;
          background: #f5f7fa;
          padding: 4px 8px;
          border-radius: 4px;
        }

        .el-tag {
          margin-left: 10px;
        }
      }
    }
  }

  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
    font-weight: 500;
  }

  .el-checkbox-group {
    .el-checkbox {
      margin-right: 15px;
      margin-bottom: 8px;
    }
  }

  .el-alert {
    margin-top: 20px;
  }
}

/* 批量设置发布状态功能样式 */
.batch-publish-container {
  .path-info-card {
    margin-bottom: 20px;

    .path-details {
      .path-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          width: 120px;
          font-weight: 500;
          color: #606266;
        }

        .value {
          flex: 1;
          color: #303133;
          font-family: 'Courier New', monospace;
          font-size: 13px;
          background: #f5f7fa;
          padding: 4px 8px;
          border-radius: 4px;
        }

        .el-tag {
          margin-left: 10px;
        }
      }
    }
  }

  .selected-files-info {
    margin-bottom: 20px;

    .file-list {
      max-height: 200px;
      overflow-y: auto;

      .file-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f2f5;

        &:last-child {
          border-bottom: none;
        }

        .file-index {
          width: 30px;
          font-weight: 500;
          color: #606266;
        }

        .file-name {
          flex: 1;
          margin-right: 10px;
          color: #303133;
        }

        .el-tag {
          margin-left: 8px;
        }
      }
    }
  }

  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
    font-weight: 500;
  }
}

/* 批量重命名功能样式 */
.batch-rename-container {
  .rename-mode-selection {
    margin-bottom: 20px;
    padding: 16px;
    background: #f5f7fa;
    border-radius: 6px;
    border: 1px solid #e4e7ed;

    .el-radio-group {
      .el-radio {
        margin-right: 30px;
        font-weight: 500;
      }
    }
  }

  .selected-files-preview {
    margin-bottom: 20px;

    .file-list {
      max-height: 200px;
      overflow-y: auto;

      .file-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f2f5;

        &:last-child {
          border-bottom: none;
        }

        .file-index {
          width: 30px;
          font-weight: 500;
          color: #606266;
        }

        .file-name {
          flex: 1;
          margin-right: 10px;
          color: #303133;
        }

        .el-tag {
          margin-left: auto;
        }
      }
    }
  }

  .rename-instructions {
    margin-bottom: 20px;
  }

  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
    font-weight: 500;
  }

  .filename-input-container {
    position: relative;

    .filename-actions {
      margin-top: 8px;
      display: flex;
      justify-content: flex-end;
    }
  }
}

/* 去水印功能样式 */
.watermark-container {
  .watermark-instructions {
    margin-bottom: 20px;
  }

  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .el-form-item {
    margin-bottom: 18px;
  }

  .el-radio-group {
    .el-radio {
      margin-right: 20px;
    }
  }

  .el-checkbox-group {
    .el-checkbox {
      margin-right: 15px;
      margin-bottom: 8px;
    }
  }
}

.watermark-progress-container {
  .progress-info {
    .current-status {
      margin: 20px 0;
      text-align: center;

      h4 {
        margin-bottom: 10px;
        color: #303133;
      }
    }

    .progress-details {
      margin: 20px 0;
    }

    .processing-files {
      margin-top: 20px;

      h4 {
        margin-bottom: 10px;
        color: #303133;
      }

      .file-results {
        max-height: 200px;
        overflow-y: auto;
        padding: 10px;
        background: #f5f7fa;
        border-radius: 4px;

        .file-result-item {
          margin-bottom: 5px;
        }
      }
    }

    .error-message {
      margin-top: 20px;
    }
  }
}

/* 视频预览相关样式 */
.file-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.video-thumbnail-container {
  flex-shrink: 0;
}

.video-thumbnail-small {
  max-width: 60px;
  max-height: 60px;
  min-width: 30px;
  min-height: 30px;
  object-fit: contain;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
  background: #f8f9fa;
}

.video-thumbnail-small:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.video-thumbnail-placeholder {
  width: 60px;
  height: 60px;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #909399;
}

.video-thumbnail-placeholder:hover {
  background: #e6f7ff;
  color: #409eff;
  transform: scale(1.05);
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-info .file-content {
  width: 100%;
}

.video-info {
  color: #606266;
  font-size: 12px;
  margin-left: 8px;
}

.video-info::before {
  content: '|';
  margin-right: 8px;
  color: #dcdfe6;
}

/* 长视频标记样式 */
.long-video-tag {
  background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%) !important;
  color: #fff !important;
  border: none !important;
  font-weight: 600 !important;
  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3) !important;
  animation: pulse-warning 2s infinite;
}

@keyframes pulse-warning {
  0% {
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
  }
  50% {
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.6);
  }
  100% {
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
  }
}

/* 长视频文件名样式 */
.long-video-name {
  font-weight: 600 !important;
  color: #ff9800 !important;
}

/* 长视频行背景高亮 */
.el-table__row:has(.long-video-tag) {
  background-color: rgba(255, 193, 7, 0.05) !important;
}

.el-table__row:has(.long-video-tag):hover {
  background-color: rgba(255, 193, 7, 0.1) !important;
}

/* 音频处理功能样式 */
.audio-processing-container {
  .processing-instructions {
    margin-bottom: 20px;

    .el-alert {
      p {
        margin: 4px 0;
        font-size: 13px;
        line-height: 1.5;
      }
    }
  }

  .processing-form {
    .el-form-item {
      margin-bottom: 20px;

      .form-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
        line-height: 1.4;
      }
    }

    .el-select,
    .el-input {
      width: 100%;
    }

    .el-slider {
      margin: 10px 0;
    }
  }
}
</style>